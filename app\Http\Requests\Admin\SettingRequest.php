<?php

namespace App\Http\Requests\Admin;

use App\Repositories\SettingRepository;
use Illuminate\Foundation\Http\FormRequest;

class SettingRequest extends FormRequest
{
    const SETTING_TYPE_NUMBER = 'number';

    const SETTING_TYPE_TEXT = 'text';

    const SETTING_TYPE_BOOLEAN = 'boolean';

    protected $adminSettings;

    public function authorize(): bool
    {
        return true;
    }

    protected function getAdminSettings()
    {
        if (! $this->adminSettings) {
            $this->adminSettings = app(SettingRepository::class)->getAllAdminSettings()->keyBy('key')->toArray();
        }

        return $this->adminSettings;
    }

    public function rules(): array
    {
        $rules = [];
        $languages = ['en', 'ar', 'ur'];

        foreach ($this->getAdminSettings() as $key => $setting) {
            foreach ($languages as $lang) {
                $field = "{$key}.{$lang}";

                switch ($setting['type']) {
                    case self::SETTING_TYPE_NUMBER:
                        $rules[$field] = ['nullable', 'numeric', 'min:0'];
                        break;
                    case self::SETTING_TYPE_TEXT:
                        $rules[$field] = ['nullable', 'string', 'max:255'];
                        break;
                    case self::SETTING_TYPE_BOOLEAN:
                        $rules[$field] = ['nullable', 'boolean'];
                        break;
                    default:
                        $rules[$field] = ['nullable'];
                }
            }
        }

        return $rules;
    }

    public function messages(): array
    {
        $messages = [];
        $languages = ['en', 'ar', 'ur'];

        foreach ($this->getAdminSettings() as $key => $setting) {
            foreach ($languages as $lang) {
                $field = "{$key}.{$lang}";
                $langLabel = match ($lang) {
                    'en' => 'English',
                    'ar' => 'Arabic',
                    'ur' => 'Urdu',
                };

                if ($setting['type'] === self::SETTING_TYPE_NUMBER) {
                    $messages["{$field}.numeric"] = __('The :language value must be a number.', ['language' => $langLabel]);
                    $messages["{$field}.min"] = __('The :language value must be at least 0.', ['language' => $langLabel]);
                }

                if ($setting['type'] === self::SETTING_TYPE_TEXT) {
                    $messages["{$field}.string"] = __('The :language value must be a string.', ['language' => $langLabel]);
                    $messages["{$field}.max"] = __('The :language value must not exceed 255 characters.', ['language' => $langLabel]);
                }

                if ($setting['type'] === self::SETTING_TYPE_BOOLEAN) {
                    $messages["{$field}.boolean"] = __('The :language value must be true or false.', ['language' => $langLabel]);
                }
            }
        }

        return $messages;
    }
}
