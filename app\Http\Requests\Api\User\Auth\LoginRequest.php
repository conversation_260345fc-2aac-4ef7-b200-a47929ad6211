<?php

namespace App\Http\Requests\Api\User\Auth;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $identifier = $this->input('identifier');
        $isEmail = filter_var($identifier, FILTER_VALIDATE_EMAIL);

        return [
            'identifier' => 'required|string',
            'password' => 'required|string|min:6',
            'country_code' => $isEmail ? 'nullable' : 'required|string|regex:/^\+[1-9]\d{1,14}$/',
            'device_id' => 'nullable|string|max:255',
            'firebase_token' => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'identifier.required' => 'Email or phone number is required',
            'password.required' => 'Password is required',
            'password.min' => 'Password must be at least 6 characters',
            'country_code.required' => 'Country code is required for phone authentication',
            'country_code.regex' => 'Country code must be in international format (e.g., +1, +20)',
        ];
    }
}
