<?php

namespace App\Http\Requests\Api\User\RentalOperator;

use App\Rules\InCountryLocation;
use App\Rules\ValidIban;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = auth('user')->user();

        return array_merge(
            $this->getLocationValidationRules($user),
            $this->getEmailValidationRules($user),
            $this->getUsernameValidationRules($user),
            $this->getBankValidationRules($user),
        );
    }

    /**
     * Get location validation rules based on user's current location status.
     *
     * Location is required only if the user doesn't already have a location set.
     */
    private function getLocationValidationRules(?object $user): array
    {
        $locationRequired = $user && ! $user->location ? 'required' : 'nullable';

        return [
            'location' => [$locationRequired, 'array', new InCountryLocation(['SA', 'EG'])],
            'location.lat' => [
                'required_with:location',
                'numeric',
                'between:-90,90',
            ],
            'location.lng' => [
                'required_with:location',
                'numeric',
                'between:-180,180',
            ],
        ];
    }

    /**
     * Get email validation rules based on user's current email status.
     */
    private function getEmailValidationRules(?object $user): array
    {
        if (! empty($user->email)) {
            return [];
        }

        return [
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
                Rule::unique('users', 'email')->ignore($user?->id),
            ],
        ];
    }

    /**
     * Get username validation rules based on user's current username status.
     */
    private function getUsernameValidationRules(?object $user): array
    {
        if (! empty($user->username)) {
            return [];
        }

        return [
            'username' => [
                'required',
                'string',
                'min:3',
                'max:20',
                'regex:/^[a-zA-Z0-9_-]+$/',
                Rule::unique('users', 'username'),
            ],

        ];
    }

    /**
     * Get bank details validation rules based on user's current bank info status.
     *
     * Each field is required only if missing in the user's profile.
     */
    private function getBankValidationRules(?object $user): array
    {
        $rules = [];

        if (empty($user->bank_account_number)) {
            $rules['bank_account_number'] = [
                'required',
                'string',
                'min:10',
                'max:14',
                Rule::unique('users', 'bank_account_number')->ignore($user?->id),
            ];
        }

        if (empty($user->bank_name)) {
            $rules['bank_name'] = [
                'required',
                'string',
                'max:100',
            ];
        }

        if (empty($user->iban)) {
            $rules['iban'] = [
                'required',
                new ValidIban,
                Rule::unique('users', 'iban')->ignore($user?->id),
            ];
        }

        return $rules;
    }
}
