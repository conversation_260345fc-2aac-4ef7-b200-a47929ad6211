<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;
use App\Models\User;
use App\Rules\UniquePhone;
use App\Rules\ValidMedia;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;
use Illuminate\Validation\Rule;

class UserRequest extends BaseFormRequest
{
    public function rules(): array
    {
        $user = $this->route('user');

        $rules = [
            'name' => ['required', 'string', 'min:4', 'max:30'],
            'phone' => ['required', 'string', new ValidPhone('966'), new UniquePhone('966', User::class, $user?->id)],
            'email' => ['nullable', 'string', 'email:rfc,dns', 'max:255', Rule::unique('users', 'email')->ignore($user?->id)],
            'location' => ['nullable', 'array'],
            'location.lat' => ['nullable', 'numeric', 'between:-90,90'],
            'location.lng' => ['nullable', 'numeric', 'between:-180,180'],
            'location.address' => ['nullable', 'string', 'max:255'],
            'bank_name' => ['nullable', 'string', 'max:150'],
            'bank_account_number' => ['nullable', 'string', 'regex:/^\d{8,18}$/', Rule::unique('users', 'bank_account_number')->ignore($user?->id)],
            'iban' => ['nullable', 'string', 'regex:/^SA\d{22}$/', Rule::unique('users', 'iban')->ignore($user?->id)],
            'image' => ['nullable', new ValidMedia(['image'])],
            'password' => ['nullable', 'string', new ValidPassword, 'confirmed'],
            'roles' => ['nullable', 'array'],
            'roles.*' => ['in:technician,supplier,rental_outlet'],
        ];

        if ($this->hasRole('supplier')) {
            $rules = array_merge($rules, [
                'supplier.type' => ['required', 'in:wholesale,retail'],
                'supplier.company_name' => ['required', 'string', 'max:255'],
                'supplier.company_email' => ['required', 'email:rfc,dns', 'max:255', Rule::unique('suppliers', 'company_email')->ignore($user?->supplier?->id)],
                'supplier.tax_number' => ['required', 'regex:/^3\d{14}$/', Rule::unique('suppliers', 'tax_number')->ignore($user?->supplier?->id)],
                'supplier.commercial_register' => ['required', 'regex:/^[1-9][0-9]{9}$/', Rule::unique('suppliers', 'commercial_register')->ignore($user?->supplier?->id)],
                'supplier.categories' => ['required', 'array'],
                'supplier.categories.*' => ['required', 'exists:categories,id'],
                'supplier.company_image' => [Rule::requiredIf(! (bool) $user), 'nullable', new ValidMedia(['image'])],
            ]);
        }

        if ($this->hasRole('technician')) {
            $rules = array_merge($rules, [
                'technician.id_number' => ['required', 'regex:/^[12]\d{9}$/', Rule::unique('technicians', 'id_number')->ignore($user?->technician?->id)],
                'technician.nationality_id' => ['required', 'exists:countries,id'],
                'technician.specializations' => ['required', 'array'],
                'technician.specializations.*' => ['required', 'exists:specializations,id'],
            ]);
        }

        // fields required if has any role
        if (count($this->input('roles', [])) > 0) {
            $rules['bank_name'][0] = 'required';
            $rules['bank_account_number'][0] = 'required';
            $rules['iban'][0] = 'required';
            $rules['location'][0] = 'required';
            $rules['location.lat'][0] = 'required';
            $rules['location.lat'][0] = 'required';
            $rules['location.address'][0] = 'required';
        }

        // Email required if supplier or rental_outlet
        if ($this->hasRole('supplier') || $this->hasRole('rental_outlet')) {
            $rules['email'][0] = 'required'; // replace 'nullable' with 'required'
        }

        // Image required if technician
        if ($this->hasRole('technician')) {
            $rules['image'][0] = Rule::requiredIf(! (bool) $user); // replace 'nullable' with 'required'
        }

        return $rules;
    }

    private function hasRole(string $role): bool
    {
        return collect($this->input('roles', []))->contains($role);
    }

    public function messages()
    {
        return [
            'location.lat.required' => __('location is required'),
            'supplier.company_email.unique' => __('validation.custom.email.unique'),
        ];
    }
}
