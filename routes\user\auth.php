<?php

use App\Http\Controllers\Api\User\AuthController;
use Illuminate\Support\Facades\Route;

Route::prefix('auth')->name('auth.')->controller(AuthController::class)->group(function () {
    Route::post('/login', 'login')->name('login');
    Route::post('/register/biometric', 'registerBiometric')->middleware('auth:sanctum')->name('register.biometric');
    Route::post('/login/biometric', 'loginWithBiometric')->name('login.biometric');
    Route::post('/login/social', 'loginWithSocial')->name('login.social');
    Route::post('/login/guest', 'loginAsGuest')->name('login.guest');
    Route::post('/register', 'register')->name('register');
    Route::post('/otp/send', 'sendOtp')->name('otp.send');
    Route::post('/otp/verify', 'verifyOtp')->name('otp.verify');
    Route::post('/reset-password', 'resetPassword')->middleware('auth:sanctum')->name('reset');
    Route::get('/logout', 'logout')->middleware('auth:sanctum')->name('logout');
    Route::post('/can-send-otp', 'canSendOtp')->name('can.send.otp');
});
