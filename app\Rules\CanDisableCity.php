<?php

namespace App\Rules;

use App\Models\City;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CanDisableCity implements ValidationRule
{
    public function __construct(private readonly ?City $city)
    {
        //
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if ($this->city && $value === 'inactive') {
            $relations = [
                'areas' => __('City has related areas and cannot be disabled.'),
                'drivers' => __('City has related drivers and cannot be disabled.'),
                'companies' => __('City has related companies and cannot be disabled.'),
                'hubs' => __('City has related hub and cannot be disabled.'),
            ];

            foreach ($relations as $relation => $message) {
                if ($this->city->$relation()->exists()) {
                    $fail($message);
                }
            }
        }
    }
}
