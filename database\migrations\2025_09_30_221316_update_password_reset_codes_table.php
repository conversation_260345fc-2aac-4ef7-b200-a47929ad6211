<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::drop('password_reset_codes');

        Schema::create('password_reset_codes', function (Blueprint $table) {
            $table->id();
            $table->string('code');
            $table->timestamp('expires_at');
            $table->morphs('user');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::drop('password_reset_codes');

        Schema::create('password_reset_codes', function (Blueprint $table) {
            $table->morphs('userable');
            $table->string('email');
            $table->string('code', 10);
            $table->integer('resend_attempts')->default(0);
            $table->timestamp('expires_at')->nullable();
            $table->timestamp('last_resend_attempt_at')->nullable();
            $table->timestamps();
        });
    }
};
