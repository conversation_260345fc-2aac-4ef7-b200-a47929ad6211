<?php

function success($data)
{
    return response()->json([
        'data' => $data,
    ]);
}

function error($data, $code = 400)
{
    return response()->json([
        'data' => $data,
    ], $code);
}

function generateUniqueCode(int $length = 8, string|array $excludeTypes = []): string
{
    if ($length < 4) {
        throw new InvalidArgumentException('Length must be at least 4 to allow character type diversity.');
    }

    if (! is_array($excludeTypes)) {
        $excludeTypes = array_map('trim', explode(',', $excludeTypes));
    }

    // Define character sets
    $sets = [
        'uppercase' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
        'lowercase' => 'abcdefghijklmnopqrstuvwxyz',
        'numbers' => '0123456789',
        'symbols' => '!@#$%^&*()-_=+[]{}<>?',
    ];

    // Exclude any types specified
    foreach ($excludeTypes as $exclude) {
        if (! array_key_exists($exclude, $sets)) {
            throw new InvalidArgumentException(sprintf("Invalid type '%s' in excluded types.", $exclude));
        }

        unset($sets[$exclude]);
    }

    if ($sets === []) {
        throw new InvalidArgumentException('At least one character type must be included.');
    }

    // Always include one character from each selected type
    $code = '';
    foreach ($sets as $chars) {
        $code .= $chars[random_int(0, strlen($chars) - 1)];
    }

    // Fill the rest of the code to reach the required length
    $allCharacters = implode('', $sets);
    for ($i = strlen($code); $i < $length; $i++) {
        $code .= $allCharacters[random_int(0, strlen($allCharacters) - 1)];
    }

    return str_shuffle($code);
}

function restoreInvalidatedValue($value)
{
    $delimiter = '_del_';
    $pos = strrpos((string) $value, $delimiter);

    return $pos !== false ? substr((string) $value, 0, $pos) : $value;
}

function formatSecondsToMinutesTime($seconds): string
{
    $minutes = floor($seconds / 60);
    $remainingSeconds = $seconds % 60;

    return sprintf('%02d:%02d', $minutes, $remainingSeconds);
}

function formatSecondsToHoursTime($seconds): string
{
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $remainingSeconds = $seconds % 60;

    return sprintf('%02d:%02d:%02d', $hours, $minutes, $remainingSeconds);
}

function statusBadge($status): ?string
{
    if ($status == 'active' || $status == 'approved') {
        return "<span class='badge bg-success'>".__($status).'</span>';
    }

    if ($status == 'pending') {
        return "<span class='badge bg-warning'>".__($status).'</span>';
    }

    if ($status == 'on_review') {
        return "<span class='badge bg-info'>".__($status).'</span>';
    }

    if ($status == 'inactive' || $status == 'rejected') {
        return "<span class='badge bg-danger'>".__($status).'</span>';
    }

    return null;
}

function normalizePhoneNumber($number): string
{
    return ltrim((string) $number, '0');
}

function formatPhone($country_code, $phone): string
{
    return "<span dir='ltr'>$country_code $phone</span>";
}

function getInvalidatedValue(?string $value): ?string
{
    if (is_null($value)) {
        return null;
    }

    return $value.'_del_'.Str::random();
}
