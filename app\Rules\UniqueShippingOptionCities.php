<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueShippingOptionCities implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Skip validation if the value isn't an array
        if (! is_array($value)) {
            return;
        }

        // Extract all city IDs from the shipping options
        $cityIds = array_column($value, 'id');

        // Keep track of cities we've already seen
        $seenCities = [];
        $duplicateCities = [];

        // Check each city ID for duplicates
        foreach ($cityIds as $cityId) {
            if (in_array($cityId, $seenCities)) {
                // We've seen this city before - it's a duplicate
                $duplicateCities[] = $cityId;
            } else {
                // First time seeing this city - add it to our list
                $seenCities[] = $cityId;
            }
        }

        // If we found any duplicates, fail the validation
        if (! empty($duplicateCities)) {
            // Clean up the attribute name for a better error message
            $fieldName = $this->getHumanReadableFieldName($attribute);
            $fail(__('You cannot select the same city multiple times for :attribute.', ['attribute' => $fieldName]));
        }
    }

    /**
     * Convert technical attribute names to human-readable format
     */
    private function getHumanReadableFieldName(string $attribute): string
    {
        // Handle nested array notation like "shipping_options.0.cities"
        if (str_contains($attribute, '.')) {
            $parts = explode('.', $attribute);
            $lastPart = end($parts);

            // Convert snake_case to readable format
            return str_replace('_', ' ', $lastPart);
        }

        // Convert snake_case to readable format for simple attributes
        return str_replace('_', ' ', $attribute);
    }
}
