<?php

namespace Database\Factories;

use App\Enums\ApprovalStatus;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\RentalOutlet>
 */
class RentalOutletFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::where('is_verified', true)->inRandomOrder()->value('id'),
            'approval_status' => fake()->randomElement(ApprovalStatus::cases()),
        ];
    }

    /**
     * Create a rental outlet with approved status.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => ApprovalStatus::APPROVED,
        ]);
    }

    /**
     * Create a rental outlet with pending status.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => ApprovalStatus::PENDING,
        ]);
    }

    /**
     * Create a rental outlet with rejected status.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => ApprovalStatus::REJECTED,
        ]);
    }
}
