<?php

namespace App\Services\User;

use App\DTO\User\LoginData;
use App\Http\Resources\User\AuthResource;
use App\Repositories\UserRepository;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Support\Facades\Hash;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class PasswordAuthService
{
    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly GuestAuthService $guestAuthService
    ) {
        //
    }

    /**
     * Authenticate user with password
     */
    public function login(LoginData $data): array
    {
        $user = $this->userRepository->findByIdentifier(
            $data->identifier,
            $data->country_code
        );

        if (! $user instanceof \App\Models\User) {
            throw new NotFoundHttpException(__('User not found'));
        }

        if (! Hash::check($data->password, $user->password)) {
            throw new AuthenticationException(__('Invalid credentials.'));
        }

        if ($data->device_id !== null && $data->device_id !== '' && $data->device_id !== '0') {
            $user->updateDeviceToken([
                'device_id' => $data->device_id,
                'firebase_token' => $data->firebase_token,
            ]);
        }

        if ($guestToken = request()->header('X-GUEST-TOKEN')) {
            $this->guestAuthService->mergeGuestIntoUser($guestToken, $user);
        }

        return [
            'user' => new AuthResource($user),
            'token' => $user->createToken('password-token')->plainTextToken,
        ];
    }
}
