<?php

namespace App\Http\Requests\Api\User\Auth;

use Illuminate\Foundation\Http\FormRequest;

class SocialLoginRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'provider' => 'required|string|in:google,facebook,apple,github',
            'provider_id' => 'required|string',
            'email' => 'required|email',
            'name' => 'required|string|max:255',
            'avatar' => 'nullable|string|url',
            'device_id' => 'nullable|string',
        ];
    }

    public function messages(): array
    {
        return [
            'provider.required' => 'Social provider is required',
            'provider.in' => 'Invalid social provider',
            'provider_id.required' => 'Provider ID is required',
            'email.required' => 'Email is required',
            'email.email' => 'Invalid email format',
            'name.required' => 'Name is required',
            'avatar.url' => 'Avatar must be a valid URL',
        ];
    }
}
