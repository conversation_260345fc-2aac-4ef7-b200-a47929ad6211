  @php
      $lat = $lat ?? 24.7136;
      $lng = $lng ?? 46.6753;
  @endphp
  
  <script src="https://maps.googleapis.com/maps/api/js?key={{config('services.google_maps.api_key')}}&libraries=places"></script>
    <script>
        let map, marker, autocomplete, selectedLatLng, selectedAddress;

        $('#openMap').on('click', function () {
            $('#mapModal').modal('show');

            setTimeout(() => {
                if (!map) {
                    const riyadh = { lat: parseFloat('{{$lat}}'), lng: parseFloat('{{$lng}}') };

                    // Init Map
                    map = new google.maps.Map(  document.getElementById("map"), {
                        center: riyadh,
                        zoom: 10,
                        mapTypeControl: false,  // remove map/satellite
                        streetViewControl: false, 
                        fullscreenControl: false 
                    });

                    // Marker
                    marker = new google.maps.Marker({
                        position: riyadh,
                        map: map,
                        draggable: true,
                    });

                   // Search Box
                    const input = document.getElementById("pac-input");
                    autocomplete = new google.maps.places.Autocomplete(input, {
                        fields: ["place_id", "geometry", "formatted_address", "name"],
                    });

                    autocomplete.setOptions({ strictBounds: false });

                    // Link autocomplete to map viewport
                    autocomplete.bindTo("bounds", map);

                    autocomplete.addListener("place_changed", function () {
                        const place = autocomplete.getPlace();

                        /* if (!place.geometry) {
                            alert("No details available for: '" + place.name + "'");
                            return;
                        } */

                        // Center and zoom map on result
                        if (place.geometry.viewport) {
                            map.fitBounds(place.geometry.viewport);
                        } else {
                            map.setCenter(place.geometry.location);
                            map.setZoom(15);
                        }

                        // Move marker
                        marker.setPosition(place.geometry.location);

                        selectedLatLng = place.geometry.location;
                        selectedAddress = place.formatted_address;
                    });

                    // Drag Marker Event
                    google.maps.event.addListener(marker, 'dragend', function (e) {
                        selectedLatLng = e.latLng;
                        selectedAddress = `Lat: ${e.latLng.lat()}, Lng: ${e.latLng.lng()}`;
                    });

                    selectedLatLng = riyadh;
                    selectedAddress = "Riyadh, Saudi Arabia";
                }

                google.maps.event.trigger(map, "resize");
                map.setCenter(marker.getPosition()); // center after resize
            }, 400);
        });

        // Save Location
        $('#saveLocation').on('click', function () {
            if (selectedLatLng) {
                const lat = typeof selectedLatLng.lat === "function" ? selectedLatLng.lat() : selectedLatLng.lat;
                const lng = typeof selectedLatLng.lng === "function" ? selectedLatLng.lng() : selectedLatLng.lng;

                $('#location').val(selectedAddress);
                $('#location-lat').val(lat);
                $('#location-lng').val(lng);
                $('#mapModal').modal('hide');
            } else {
                alert("{{ __('Please select a location on the map') }}");
            }
        });
    </script>