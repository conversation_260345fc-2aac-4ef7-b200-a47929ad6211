<?php

namespace App\DTO\Supplier;

use App\Enums\ApprovalStatus;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Spatie\LaravelData\Data;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class UpgradeSupplierData extends Data
{
    public function __construct(
        public ?string $username,
        public ?string $email,
        public ?string $bank_account_number,
        public ?string $bank_name,
        public ?string $iban,
        public ?array $location,
        public string $type,
        public string $company_name,
        public string $company_email,
        public string $tax_number,
        public string $commercial_register,
        public array $categories,
        public ?UploadedFile $company_logo,
    ) {}

    /**
     * Extract fields that belong to the User model.
     */
    public function userData(): array
    {
        $data = array_filter([
            'username' => $this->username,
            'email' => $this->email,
            'bank_account_number' => $this->bank_account_number,
            'bank_name' => $this->bank_name,
            'iban' => $this->iban,
        ], fn ($value) => ! is_null($value));

        if ($this->location) {
            $data['location'] = new Point(
                $this->location['lat'],
                $this->location['lng']
            );
        }

        return $data;
    }

    /**
     * Extract fields that belong to RentalOperator model.
     */
    public function supplierData(): array
    {
        return [
            'user_id' => auth('user')->id(),
            'type' => $this->type,
            'company_name' => $this->company_name,
            'company_email' => $this->company_email,
            'tax_number' => $this->tax_number,
            'commercial_register' => $this->commercial_register,
            'categories' => $this->categories,
            'company_logo' => $this->company_logo,
            'approval_status' => ApprovalStatus::PENDING,
        ];
    }
}
