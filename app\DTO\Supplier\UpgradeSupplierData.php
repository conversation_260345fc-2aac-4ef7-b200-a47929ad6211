<?php

namespace App\DTO\Supplier;

use <PERSON><PERSON>\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Attributes\Validation\Unique;
use Spatie\LaravelData\Attributes\Validation\Exists;
use Spatie\LaravelData\Attributes\Validation\Email;
use Spatie\LaravelData\Attributes\Validation\Min;
use Spatie\LaravelData\Attributes\Validation\Max;
use Spatie\LaravelData\Attributes\Validation\In;
use App\Enums\SupplierApprovalStatus;
use Spatie\LaravelData\Data;
use App\Enums\SupplierType;

class UpgradeSupplierData extends Data
{
    public function __construct(
        #[Required, In([SupplierType::WHOLESALE->value, SupplierType::RETAIL->value])]
        public string $type,

        #[Required, Max(255)]
        public string $company_name,

        #[Required, Email, Unique('suppliers', 'company_email')]
        public string $company_email,

        #[Required, Unique('suppliers', 'tax_number')]
        public string $tax_number,

        #[Required, Max(255)]
        public string $commercial_register,

        #[Required, Min(1)]
        /** @var array<int> */
        #[Exists('categories', 'id')]
        public array $categories,
    ) {}

    /**
     * Convert DTO into array for Supplier creation.
     */
    public function toSupplierArray(int $userId): array
    {
        return [
            'user_id' => $userId,
            'type' => $this->type,
            'company_name' => $this->company_name,
            'company_email' => $this->company_email,
            'tax_number' => $this->tax_number,
            'commercial_register' => $this->commercial_register,
            'approval_status' => SupplierApprovalStatus::PENDING->value,
        ];
    }
}
