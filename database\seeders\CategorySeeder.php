<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => ['en' => 'Cement & Concrete', 'ar' => 'الاسمنت والخرسانة'],
                'children' => [
                    ['name' => ['en' => 'Portland Cement', 'ar' => 'أسمنت بورتلاند']],
                    ['name' => ['en' => 'Ready-Mix Concrete', 'ar' => 'خرسانة جاهزة']],
                    ['name' => ['en' => 'Blocks & Bricks', 'ar' => 'بلوك وطوب']],
                ],
            ],
            [
                'name' => ['en' => 'Steel & Metals', 'ar' => 'الحديد والمعادن'],
                'children' => [
                    ['name' => ['en' => 'Rebars', 'ar' => 'حديد تسليح']],
                    ['name' => ['en' => 'Steel Sheets', 'ar' => 'صفائح فولاذية']],
                ],
            ],
            [
                'name' => ['en' => '<PERSON> & Carpentry', 'ar' => 'الخشب والنجارة'],
                'children' => [
                    ['name' => ['en' => 'Plywood', 'ar' => 'خشب رقائقي']],
                    ['name' => ['en' => 'Doors & Frames', 'ar' => 'أبواب وإطارات']],
                ],
            ],
            [
                'name' => ['en' => 'Plumbing Materials', 'ar' => 'مواد السباكة'],
                'children' => [
                    ['name' => ['en' => 'Pipes & Fittings', 'ar' => 'أنابيب ووصلات']],
                    ['name' => ['en' => 'Valves & Taps', 'ar' => 'صمامات وصنابير']],
                    ['name' => ['en' => 'Water Tanks', 'ar' => 'خزانات مياه']],
                ],
            ],
            [
                'name' => ['en' => 'Electrical Materials', 'ar' => 'المواد الكهربائية'],
                'children' => [
                    ['name' => ['en' => 'Wires & Cables', 'ar' => 'أسلاك وكابلات']],
                    ['name' => ['en' => 'Switches & Sockets', 'ar' => 'مفاتيح ومقابس']],
                    ['name' => ['en' => 'Lighting Fixtures', 'ar' => 'إضاءات']],
                ],
            ],
        ];

        foreach ($categories as $cat) {
            $category = Category::create([
                'name' => $cat['name'],
                'type' => 'main',
            ]);

            foreach ($cat['children'] as $child) {
                Category::create([
                    'name' => $child['name'],
                    'type' => 'sub',
                    'parent_id' => $category->id,
                ]);
            }
        }
    }
}
