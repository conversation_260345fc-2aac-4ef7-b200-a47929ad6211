<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;
use App\Rules\ValidPassword;
use Illuminate\Contracts\Validation\Validator;

class ResetPasswordRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'password' => ['required', 'string', new ValidPassword, 'confirmed'],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        session()->flash('username', $this->username);
        session()->flash('otp', $this->otp);

        parent::failedValidation($validator);
    }
}
