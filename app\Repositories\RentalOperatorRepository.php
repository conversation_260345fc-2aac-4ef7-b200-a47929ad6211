<?php

namespace App\Repositories;

use App\Models\RentalOutlet;

class RentalOperatorRepository
{
    public function __construct(private readonly RentalOutlet $model)
    {
        //
    }

    /**
     * Find a rental outlet by user ID.
     */
    public function findByUserId(int $userId): ?RentalOutlet
    {
        return $this->model->where('user_id', $userId)->first();
    }

    /**
     * Create a new rental outlet.
     */
    public function create(array $data): RentalOutlet
    {
        return $this->model->create($data);
    }
}
