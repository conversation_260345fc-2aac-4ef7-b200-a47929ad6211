<?php

namespace App\Rules;

use App\Repositories\HubRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class SameHubCompany implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $companyId = auth('company')->user()->company_id;
        $hub = app(HubRepository::class)->getById($value);

        if ($hub->company_id != $companyId) {
            $fail(__('The selected hub is invalid.'));
        }
    }
}
