<?php

namespace App\Services\User;

use App\DTO\User\BiometricLoginData;
use App\Http\Resources\User\AuthResource;
use App\Repositories\UserRepository;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class BiometricAuthService
{
    public function __construct(
        private readonly UserRepository $userRepository
    ) {
        //
    }

    public function register(string $biometricToken): string
    {
        $user = auth('user')->user();

        $user->update([
            'biometric_token' => $biometricToken,
            'biometric_enabled' => true,
        ]);

        return __('Biometric registered successfully.');
    }

    /**
     * Authenticate user with biometric token
     */
    public function login(BiometricLoginData $data): array
    {
        $user = $this->userRepository->findByBiometricToken($data->biometric_token);

        if (! $user instanceof \App\Models\User) {
            throw new NotFoundHttpException(__('User not found'));
        }

        if ($data->device_id !== null && $data->device_id !== '' && $data->device_id !== '0') {
            $user->updateDeviceToken([
                'device_id' => $data->device_id,
                'firebase_token' => $data->firebase_token,
            ]);
        }

        return [
            'user' => new AuthResource($user),
            'token' => $user->createToken('biometric-token')->plainTextToken,
        ];
    }
}
