<?php

namespace App\Services\Firebase;

use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Contract\Messaging;
use Kreait\Firebase\Exception\Messaging\InvalidArgument;
use Kreait\Firebase\Exception\Messaging\NotFound;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

class FirebaseService
{
    public function __construct(
        private readonly Messaging $messaging
    ) {}

    /**
     * Send a Firebase Cloud Message notification to a notifiable entity.
     *
     * @param  object  $notifiable  Must have `fcm_token` and optionally `app_locale`
     * @param  array<string, mixed>  $data
     */
    public function send(object $notifiable, array $data): void
    {
        if (! $this->canSendTo($notifiable)) {
            return;
        }

        $locale = $this->resolveLocale($notifiable, $data);

        $message = CloudMessage::new()
            ->toToken($notifiable->fcm_token)
            ->withNotification(Notification::create(
                $data['title'][$locale] ?? '',
                $data['body'][$locale] ?? ''
            ))
            ->withData($this->flattenData($data));

        try {
            $this->messaging->send($message);
        } catch (NotFound|InvalidArgument $e) {
            $this->handleInvalidToken($notifiable);
        } catch (\Throwable $e) {
            $this->logError($notifiable, $e);
        }
    }

    private function canSendTo(object $notifiable): bool
    {
        return ! empty($notifiable->fcm_token) && $notifiable->push_notifications_enabled;
    }

    private function resolveLocale(object $notifiable, array $data): string
    {
        $locale = $notifiable->app_locale ?? app()->getLocale();

        if (! isset($data['title'][$locale], $data['body'][$locale])) {
            $locale = app()->getLocale();
        }

        return $locale;
    }

    private function flattenData(array $data): array
    {
        $flattened = [];

        foreach ($data as $key => $value) {
            if (is_array($value)) {
                foreach ($value as $subKey => $subValue) {
                    $flattened["{$key}_{$subKey}"] = (string) $subValue;
                }
            } else {
                $flattened[$key] = (string) $value;
            }
        }

        return $flattened;
    }

    private function handleInvalidToken(object $notifiable): void
    {
        if (method_exists($notifiable, 'update')) {
            $notifiable->update(['fcm_token' => null]);
        }
    }

    private function logError(object $notifiable, \Throwable $e): void
    {
        Log::error('FCM send failed', [
            'token' => $notifiable->fcm_token,
            'error' => $e->getMessage(),
        ]);
    }
}
