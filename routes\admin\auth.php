<?php

use App\Http\Controllers\Admin\Auth\AuthenticationController;
use App\Http\Controllers\Admin\Auth\PasswordResetController;
use Illuminate\Support\Facades\Route;

Route::middleware('guest:admin')->group(function () {
    Route::get('login', [AuthenticationController::class, 'loginView'])->name('login');
    Route::post('login', [AuthenticationController::class, 'login']);
    Route::get('forgot-password', [PasswordResetController::class, 'forgotPassword'])->name('forgot-password');
    Route::post('forgot-password', [PasswordResetController::class, 'sendResetCode']);
    Route::get('confirm-otp', [AuthenticationController::class, 'confirmOtpView'])->name('confirm-otp');
    Route::post('confirm-otp', [AuthenticationController::class, 'confirmOtp']);
    Route::post('resend-otp', [AuthenticationController::class, 'resendOtp'])->name('resend-otp');
    Route::get('confirm-reset-code', [PasswordResetController::class, 'confirmResetCodeView'])->name('confirm-reset-code');
    Route::post('confirm-reset-code', [PasswordResetController::class, 'confirmResetCode']);
    Route::post('resend-reset-code', [PasswordResetController::class, 'resendResetCode'])->name('resend-reset-code');
    Route::get('reset-password', [PasswordResetController::class, 'resetPasswordView'])->name('reset-password');
    Route::post('reset-password', [PasswordResetController::class, 'resetPassword']);
});

Route::get('logout', [AuthenticationController::class, 'logout'])->name('logout')->middleware('auth:admin');
