<?php

namespace App\Services\Admin;

use App\DTO\Admin\GroupNotificationData;
use App\Models\GroupNotification as NotificationModel;
use App\Notifications\GroupNotification;
use App\Repositories\GroupNotificationRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class GroupNotificationService
{
    public function __construct(private readonly GroupNotificationRepository $groupNotificationRepository)
    {
        //
    }

    public function index(array $filters = [])
    {
        return $this->groupNotificationRepository->getPaginated($filters);
    }

    public function find(int $id): ?object
    {
        $notification = $this->groupNotificationRepository->find($id);

        if (!$notification instanceof NotificationModel) {
            throw new NotFoundHttpException;
        }

        return $notification;
    }

    protected function resolveModelClass(string $type): string
    {
        return match (strtolower($type)) {
            'user' => \App\Models\User::class,
            default => throw new \InvalidArgumentException("Unsupported receiver type: $type"),
        };
    }

    public function create(GroupNotificationData $data): NotificationModel
    {
        return DB::transaction(function () use ($data): NotificationModel {
            $notification = $this->groupNotificationRepository->create($data->toArray());

            $this->syncReceivers($notification, $data->receivers);
            $this->dispatchNotifications($notification);

            return $notification;
        });
    }

    public function update(GroupNotificationData $data, int $id): NotificationModel
    {
        $notification = $this->find($id);

        return DB::transaction(function () use ($notification, $data): ?object {
            $notification->update($data->toArray());
            $notification->receivers()->delete();
            $this->syncReceivers($notification, $data->receivers);
            $this->dispatchNotifications($notification);

            return $notification;
        });
    }

    protected function syncReceivers(NotificationModel $notification, array $receivers): void
    {
        $receiverData = collect($receivers)->map(fn(array $receiver): array => [
            'group_notification_id' => $notification->id,
            'receiverable_type' => $this->resolveModelClass($receiver['type']),
            'receiverable_id' => $receiver['id'],
        ])->toArray();

        $notification->receivers()->createMany($receiverData);
    }

    protected function dispatchNotifications(NotificationModel $notification): void
    {
        foreach ($notification->receivers as $receiver) {
            $notifiable = $receiver->receiverable;

            Notification::send($notifiable, new GroupNotification(
                $notification->title,
                $notification->body,
                $notification->types
            ));
        }
    }

    public function delete(int $id): void
    {
        $notification = $this->find($id);

        $notification->delete();
    }
}
