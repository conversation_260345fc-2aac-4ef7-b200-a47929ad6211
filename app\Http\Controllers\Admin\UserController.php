<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\UsersDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UserRequest;
use App\Models\User;
use App\Repositories\CategoryRepository;
use App\Repositories\CountryRepository;
use App\Repositories\SpecializationRepository;
use App\Services\Admin\UserService;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Routing\Controllers\Middleware;

class UserController extends Controller implements HasMiddleware
{
    public function __construct(
        private readonly UserService $userService,
        private readonly CountryRepository $countryRepository,
        private readonly SpecializationRepository $specializationRepository,
        private readonly CategoryRepository $categoryRepository,
    ) {}

    public static function middleware(): array
    {
        return [
            new Middleware('can:show users', only: ['index']),
            new Middleware('can:view user', only: ['show']),
            new Middleware('can:create user', only: ['create', 'store']),
            new Middleware('can:update user', only: ['edit', 'update']),
            new Middleware('can:delete user', only: ['destroy']),
        ];
    }

    public function index(UsersDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.users.index');
    }

    public function create()
    {
        $countries = $this->countryRepository->getForSelect();
        $specializations = $this->specializationRepository->all();
        $categories = $this->categoryRepository->all();

        return view('pages.admin.users.create', compact('countries', 'specializations', 'categories'));
    }

    public function store(UserRequest $request)
    {
        $this->userService->create($request->validated());

        return to_route('admin.users.index')->with('success', __('Created successfully'));
    }

    public function show(User $user)
    {
        return view('pages.admin.users.show', compact('user'));
    }

    public function edit(User $user)
    {
        $countries = $this->countryRepository->getForSelect();
        $specializations = $this->specializationRepository->all();
        $categories = $this->categoryRepository->all();

        return view('pages.admin.users.edit', compact('user', 'countries', 'specializations', 'categories'));
    }

    public function update(UserRequest $request, User $user)
    {
        $this->userService->update($user, $request->validated());

        return back()->with('success', __('Updated successfully'));
    }

    public function destroy(User $user)
    {
        $this->userService->delete($user);

        return to_route('admin.users.index')->with('success', __('Deleted successfully'));
    }

    public function toggleStatus(User $user)
    {
        $this->userService->toggleStatus($user);

        return to_route('admin.users.index')->with('success', __('Status Updated successfully'));
    }
}
