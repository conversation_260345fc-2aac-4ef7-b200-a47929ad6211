<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\SettingRequest;
use App\Services\Admin\SettingService;

class SettingController extends Controller
{
    public function __construct(private readonly SettingService $settingService) {}

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $settings = $this->settingService->getAllAdminSettings();

        return view('pages.admin.settings.index', compact('settings'));
    }

    public function update(SettingRequest $request)
    {
        $this->settingService->updateAdminSettings($request->validated());

        return redirect()->route('admin.settings.index')
            ->with('success', __('Settings updated successfully.'));
    }
}
