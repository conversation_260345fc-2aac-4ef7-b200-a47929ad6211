<?php

namespace App\Http\Controllers\Api\User;

use App\DTO\RentalOperator\StoreRentalOperatorData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\RentalOperator\StoreRequest;
use App\Services\User\RentalOperatorService;

class RentalOperatorController extends Controller
{
    public function __construct(private readonly RentalOperatorService $rentalOperatorService)
    {
        //
    }

    public function store(StoreRequest $request)
    {
        $dto = StoreRentalOperatorData::from($request->validated());
        $this->rentalOperatorService->create($dto);

        return success(__('Your account upgrade request has been sent successfully.'));
    }
}
