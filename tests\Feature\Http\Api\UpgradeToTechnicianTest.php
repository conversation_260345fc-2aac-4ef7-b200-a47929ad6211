<?php

use App\Enums\ApprovalStatus;
use App\Models\Technician;
use App\Models\User;
use App\Models\Country;
use App\Models\Specialization;
use Database\Seeders\WorldTableSeeder;
use Database\Seeders\SpecializationSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use MatanYadaev\EloquentSpatial\Objects\Point;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->baseUrl = '/api/user/profile/upgrade-to-technician';
    $this->user = makeUser(); // default user for validation tests etc.
    $this->seed(WorldTableSeeder::class);
    $this->seed(SpecializationSeeder::class);
});

/**
 * Helper to create a verified user with optional overrides.
 */
function makeUser(array $overrides = []): User
{
    return User::factory()
        ->verified()
        ->create($overrides)
        ->fresh();
}

test('user can upgrade to technician with complete data', function () {
    Storage::fake('public');

    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(2)->pluck('id')->toArray();

    $user = makeUser([
        'email' => null,
        'bank_account_number' => null,
        'bank_name' => null,
        'iban' => null,
        'location' => null,
    ]);

    $requestData = [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ];

    $response = $this->actingAs($user)->postJson($this->baseUrl, $requestData);

    $response->assertSuccessful()->assertJsonStructure(['data']);

    $user->refresh();
    expect($user->email)->toBe('<EMAIL>')
        ->and($user->bank_account_number)->toBe('**********')
        ->and($user->bank_name)->toBe('Test Bank')
        ->and($user->iban)->toBe('************************')
        ->and($user->location->latitude)->toBe(24.7136)
        ->and($user->location->longitude)->toBe(46.6753);

    expect($user->pendingTechnician)->toBeInstanceOf(Technician::class)
        ->and($user->pendingTechnician->approval_status)->toBe(ApprovalStatus::PENDING)
        ->and($user->pendingTechnician->id_number)->toBe('**********')
        ->and($user->pendingTechnician->nationality_id)->toBe($country->id);
});

test('user can upgrade to technician with partial data', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $user = makeUser([
        'email' => '<EMAIL>',
        'bank_account_number' => 'existing123',
        'location' => new Point(30.0444, 31.2357),
    ]);

    // Add a profile image to the user
    Storage::fake('public');
    $user->addMediaFromUrl('https://via.placeholder.com/150')
        ->toMediaCollection('users');

    $requestData = [
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_name' => 'New Bank',
        'iban' => 'EG********************12',
        'specializations' => $specializations,
    ];

    $response = $this->actingAs($user)->postJson($this->baseUrl, $requestData);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->email)->toBe('<EMAIL>')
        ->and($user->bank_account_number)->toBe('existing123')
        ->and($user->bank_name)->toBe('New Bank')
        ->and($user->iban)->toBe('EG********************12')
        ->and($user->location->latitude)->toBe(30.0444)
        ->and($user->location->longitude)->toBe(31.2357);

    expect($user->pendingTechnician)->toBeInstanceOf(Technician::class)
        ->and($user->pendingTechnician->id_number)->toBe('**********')
        ->and($user->pendingTechnician->nationality_id)->toBe($country->id);
});

test('user cannot upgrade if they already have an approved technician', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $user = makeUser([
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => new Point(24.7136, 46.6753),
    ]);

    // Add a profile image to the user
    Storage::fake('public');
    $user->addMediaFromUrl('https://via.placeholder.com/150')
        ->toMediaCollection('users');

    Technician::factory()->approved()->for($user)->create();

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'specializations' => $specializations,
    ]);

    $response->assertStatus(400)
        ->assertJson([
            'data' => 'You already have an approved technician upgrade request.',
        ]);
});

test('user cannot upgrade if they already have a pending technician', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $user = makeUser([
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => new Point(24.7136, 46.6753),
    ]);

    // Add a profile image to the user
    Storage::fake('public');
    $user->addMediaFromUrl('https://via.placeholder.com/150')
        ->toMediaCollection('users');

    Technician::factory()->pending()->for($user)->create();

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'specializations' => $specializations,
    ]);

    $response->assertStatus(400)
        ->assertJson([
            'data' => 'You already have a pending technician upgrade request.',
        ]);
});

test('validation fails when required fields are missing', function () {
    $user = makeUser([
        'email' => null,
        'bank_account_number' => null,
        'bank_name' => null,
        'iban' => null,
        'location' => null,
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, []);

    $response->assertStatus(422)
        ->assertJsonValidationErrors([
            'email',
            'id_number',
            'nationality_id',
            'bank_account_number',
            'bank_name',
            'iban',
            'location',
            'image',
            'specializations',
        ]);
});

test('validation fails for invalid email format', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $user = makeUser(['email' => null]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => 'invalid-email',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['email']);
});

test('validation fails for duplicate email', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $user = makeUser(['email' => null]);
    makeUser(['email' => '<EMAIL>']);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>', // duplicate
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['email']);
});

test('validation fails for duplicate id number', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    // Create another user with a technician that has the same id_number
    $existingUser = makeUser();
    Technician::factory()->for($existingUser)->create(['id_number' => '**********']);

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********', // duplicate
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['id_number']);
});

test('validation fails for duplicate bank account number', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    makeUser(['bank_account_number' => '**********']);

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********', // duplicate
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['bank_account_number']);
});

test('validation fails for duplicate iban', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    makeUser(['iban' => '************************']);

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************', // duplicate
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['iban']);
});

test('validation fails for invalid id number format', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '*********', // only 9 digits, should be 10
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['id_number']);
});

test('validation fails for invalid nationality', function () {
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => 99999, // non-existent country
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['nationality_id']);
});

test('validation fails for invalid specializations', function () {
    $country = Country::where('is_active', true)->first();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => [99999], // non-existent specialization
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['specializations.0']);
});

test('validation fails for too many specializations', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(4)->pluck('id')->toArray(); // more than max 3

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['specializations']);
});

test('validation fails for empty specializations', function () {
    $country = Country::where('is_active', true)->first();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => [], // empty array
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['specializations']);
});

test('validation fails for invalid location coordinates', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 91, 'lng' => 46.6753], // invalid latitude
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['location.lat']);
});

test('validation fails for location outside allowed countries', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 40.7128, 'lng' => -74.0060], // New York (outside SA/EG)
        'image' => UploadedFile::fake()->image('profile.jpg'),
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['location']);
});

test('user with existing location does not need to provide location again', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $user = makeUser([
        'location' => new Point(24.7136, 46.6753),
    ]);

    // Add a profile image to the user
    Storage::fake('public');
    $user->addMediaFromUrl('https://via.placeholder.com/150')
        ->toMediaCollection('users');

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'specializations' => $specializations,
        // no location provided
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->location->latitude)->toBe(24.7136)
        ->and($user->location->longitude)->toBe(46.6753);
});

test('user with existing email does not need to provide email again', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $user = makeUser([
        'email' => '<EMAIL>',
    ]);

    // Add a profile image to the user
    Storage::fake('public');
    $user->addMediaFromUrl('https://via.placeholder.com/150')
        ->toMediaCollection('users');

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'specializations' => $specializations,
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->email)->toBe('<EMAIL>');
});

test('user with existing bank details does not need to provide them again', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $user = makeUser([
        'bank_account_number' => '**********',
        'bank_name' => 'Existing Bank',
        'iban' => '************************',
    ]);

    // Add a profile image to the user
    Storage::fake('public');
    $user->addMediaFromUrl('https://via.placeholder.com/150')
        ->toMediaCollection('users');

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'specializations' => $specializations,
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->bank_account_number)->toBe('**********')
        ->and($user->bank_name)->toBe('Existing Bank')
        ->and($user->iban)->toBe('************************');
});

test('user with existing profile image does not need to provide image again', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $user = makeUser();

    // Add a profile image to the user
    Storage::fake('public');
    $user->addMediaFromUrl('https://via.placeholder.com/150')
        ->toMediaCollection('users');

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'specializations' => $specializations,
        // no image provided
    ]);

    $response->assertSuccessful();

    expect($user->hasMedia('users'))->toBeTrue();
});

test('validation fails for invalid image file', function () {
    $country = Country::where('is_active', true)->first();
    $specializations = Specialization::take(1)->pluck('id')->toArray();

    $user = makeUser([
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => new Point(24.7136, 46.6753),
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'id_number' => '**********',
        'nationality_id' => $country->id,
        'image' => UploadedFile::fake()->create('document.pdf', 100), // not an image
        'specializations' => $specializations,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['image']);
});
