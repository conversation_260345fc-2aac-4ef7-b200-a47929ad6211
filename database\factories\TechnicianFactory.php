<?php

namespace Database\Factories;

use App\Enums\ApprovalStatus;
use App\Models\Country;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Technician>
 */
class TechnicianFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::where('is_verified', true)->inRandomOrder()->value('id'),
            'id_number' => fake()->unique()->numberBetween(1000000000, 9999999999),
            'nationality_id' => Country::where('is_active', true)->inRandomOrder()->value('id'),
            'approval_status' => fake()->randomElement(ApprovalStatus::cases()),
        ];
    }

    /**
     * Create a technician with approved status.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => ApprovalStatus::APPROVED,
        ]);
    }

    /**
     * Create a technician with pending status.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => ApprovalStatus::PENDING,
        ]);
    }

    /**
     * Create a technician with rejected status.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => ApprovalStatus::REJECTED,
        ]);
    }
}
