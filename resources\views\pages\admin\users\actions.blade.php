@php
    $canShow = auth('admin')->user()->can('view user');
    $canEdit = auth('admin')->user()->can('update user');
    $canDelete = auth('admin')->user()->can('delete user');
@endphp

<div class="actions">
    @if ($canShow || $canEdit || $canDelete)
        <div class="dropdown">
            <button class="dropdown-toggle" type="button" id="actionDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="ti ti-dots"></i>
            </button>
            <ul class="dropdown-menu" aria-labelledby="actionDropdown">
                @if ($canShow)
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{{ route('admin.users.show', $user) }}"><i class="ti ti-eye"></i>{{ __('view') }}</a></li>
                @endif
                @if ($canEdit)
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{{ route('admin.users.edit', $user) }}"><i class="ti ti-edit"></i>{{ __('edit') }}</a></li>
                    <li>
                        
                        <form action="{{ route('admin.users.toggle-status', $user) }}" method="POST" style="display:inline;">
                            @csrf
                            <button type="submit" class="dropdown-item"><i class="ti ti-toggle-right"></i>{{ $user->is_active ? __('deactivate') : __('activate') }}</button>
                        </form>
                    </li>
                @endif
                @if ($canDelete)
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="javascript:void(0)" data-bs-toggle="modal" data-bs-target="#delete-modal" 
                           onclick="changeDeleteModalData(this)" delete-route="{{ route('admin.users.destroy', $user) }}"
                           delete-name="{{ __('User') }} : {{ $user->name }}">
                            <i class="ti ti-archive"></i>{{ __('delete') }}
                        </a>
                    </li>
                @endif
            </ul>
        </div>
    @else
        -
    @endif
</div>

<style>
    .dropdown-toggle {
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
    }
    .dropdown-menu {
        min-width: auto;
    }
    .dropdown-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
    }
    .dropdown-item button {
        background: none;
        border: none;
        width: 100%;
        text-align: left;
        cursor: pointer;
    }
</style>