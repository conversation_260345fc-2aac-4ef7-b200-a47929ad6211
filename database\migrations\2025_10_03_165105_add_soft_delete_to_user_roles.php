<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('suppliers', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('technicians', function (Blueprint $table) {
            $table->softDeletes();
        });

        Schema::table('rental_outlets', function (Blueprint $table) {
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('suppliers', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('technicians', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('rental_outlets', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
    }
};
