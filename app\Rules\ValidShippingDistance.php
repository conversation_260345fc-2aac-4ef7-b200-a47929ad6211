<?php

namespace App\Rules;

use App\Models\Address;
use App\Models\City;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidShippingDistance implements ValidationRule
{
    public function __construct(
        protected int $shippingTypeId,
        protected ?int $pickupAddressId,
        protected ?array $pickupLocation,
        protected array $items
    ) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $pickupDetails = $this->getLocationDetails($this->pickupAddressId, $this->pickupLocation);

        if ($pickupDetails === null || $pickupDetails === []) {
            $fail(__('Unable to determine pickup location details.'));

            return;
        }

        foreach ($this->items as $index => $item) {
            $dropoffDetails = $this->getLocationDetails(
                $item['dropoff_address_id'] ?? null,
                $item['dropoff_location'] ?? null
            );

            if ($dropoffDetails === null || $dropoffDetails === []) {
                $fail(__('Item #:item: Unable to determine dropoff location details.', ['item' => $index + 1]));

                continue;
            }

            // ✅ تحقق من تطابق الدولة الجغرافية مع dropoff_country_id
            if (isset($item['dropoff_country_id']) && $dropoffDetails['country_id'] && (int) $item['dropoff_country_id'] !== (int) $dropoffDetails['country_id']) {
                $fail(__('Item #:item: Dropoff country does not match the dropoff location.', ['item' => $index + 1]));

                continue;
            }

            // ✅ Validate shipping rule
            $validationResult = $this->validateShippingDistance($pickupDetails, $dropoffDetails, $index + 1);
            if ($validationResult !== true) {
                $fail($validationResult);
            }
        }
    }

    protected function getLocationDetails(?int $addressId, ?array $location): ?array
    {
        if ($addressId !== null && $addressId !== 0) {
            $address = Address::with(['area.city.country', 'area', 'area.city'])->find($addressId);

            if (! $address || ! $address->location) {
                return null;
            }

            return [
                'country_id' => $address->area->city->country->id ?? null,
                'country_code' => $address->area->city->country->abbv ?? 'AE',
                'city_id' => $address->area->city->id,
                'area_id' => $address->area_id,
                'coordinates' => $address->location,
            ];
        }

        if ($location && isset($location['lat'], $location['lng'])) {
            $lng = (float) $location['lng'];
            $lat = (float) $location['lat'];
            $pointWKT = "POINT($lng $lat)";

            $city = City::with('country')
                ->where('status', 'active')
                ->whereRaw('ST_Contains(area, ST_GeomFromText(?))', [$pointWKT])
                ->first();

            if (! $city) {
                return null;
            }

            $area = $city->areas()
                ->where('status', 'active')
                ->whereRaw('ST_Contains(area, ST_GeomFromText(?))', [$pointWKT])
                ->first();

            return [
                'country_id' => $city->country->id ?? null,
                'country_code' => $city->country->abbv ?? 'AE',
                'city_id' => $city->id,
                'area_id' => $area?->id,
                'coordinates' => $location,
            ];
        }

        return null;
    }

    protected function validateShippingDistance(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        return match ($this->shippingTypeId) {
            1 => $this->validateImmediateShipping($pickupDetails, $dropoffDetails, $itemNumber),
            2 => $this->validateIntercityShipping($pickupDetails, $dropoffDetails, $itemNumber),
            3 => $this->validateInternationalShipping($pickupDetails, $dropoffDetails, $itemNumber),
            default => __('Invalid shipping type.')
        };
    }

    protected function isUaeLocation(array $locationDetails): bool
    {
        return isset($locationDetails['country_code']) && $locationDetails['country_code'] === 'AE';
    }

    protected function validateImmediateShipping(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        $isUae = $this->isUaeLocation($pickupDetails) && $this->isUaeLocation($dropoffDetails);

        // For immediate shipping, both locations must be in the UAE
        // if (! $isUae) {
        //     return __('Item #:item: Immediate shipping requires both pickup and dropoff locations to be within the UAE.', [
        //         'item' => $itemNumber,
        //     ]);
        // }

        $sameArea = $pickupDetails['area_id'] && $pickupDetails['area_id'] === $dropoffDetails['area_id'];
        $sameCity = $pickupDetails['city_id'] === $dropoffDetails['city_id'];

        if (! ($sameArea || $sameCity)) {
            return __('Item #:item: Immediate shipping requires pickup and dropoff to be in the same city or area within the UAE.', [
                'item' => $itemNumber,
            ]);
        }

        return true;
    }

    protected function validateIntercityShipping(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        $isUae = $this->isUaeLocation($pickupDetails) && $this->isUaeLocation($dropoffDetails);

        // For intercity shipping, both locations must be in the UAE
        // if (! $isUae) {
        //     return __('Item #:item: Intercity shipping requires both pickup and dropoff locations to be within the UAE.', [
        //         'item' => $itemNumber,
        //     ]);
        // }

        // if ($pickupDetails['city_id'] === $dropoffDetails['city_id']) {
        //     return __('Item #:item: Intercity shipping requires pickup and dropoff to be in different cities.', ['item' => $itemNumber]);
        // }

        if ($pickupDetails['country_code'] !== $dropoffDetails['country_code']) {
            return __('Item #:item: Intercity shipping requires pickup and dropoff to be in the same country.', ['item' => $itemNumber]);
        }

        return true;
    }

    protected function validateInternationalShipping(array $pickupDetails, array $dropoffDetails, int $itemNumber): string|bool
    {
        $isPickupUae = $this->isUaeLocation($pickupDetails);
        $isDropoffUae = $this->isUaeLocation($dropoffDetails);
        $isUae = $isPickupUae && $isDropoffUae;

        // For international shipping, at least one location must be outside the UAE
        // if ($isUae) {
        //     return __('Item #:item: International shipping requires at least one location to be outside the UAE.', [
        //         'item' => $itemNumber,
        //     ]);
        // }

        if ($pickupDetails['country_code'] === $dropoffDetails['country_code']) {
            return __('Item #:item: International shipping requires pickup and dropoff to be in different countries.', ['item' => $itemNumber]);
        }

        return true;
    }
}
