<?php

use App\Http\Controllers\Provider\HomeController;
use App\Http\Controllers\Provider\ProfileController;
use Illuminate\Support\Facades\Route;

include base_path('routes/provider/auth.php');

Route::group(
    ['middleware' => ['auth:provider', 'checkStatus']],
    function () {
        Route::get('/', [HomeController::class, 'index'])->name('index');

        Route::get('profile', [ProfileController::class, 'index'])->name('profile');
        Route::post('profile', [ProfileController::class, 'update']);
    }
);
