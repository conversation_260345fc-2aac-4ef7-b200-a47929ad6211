<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Spatie\MediaLibrary\HasMedia;

class MediaUploaderService
{
    public function upload(
        Model&HasMedia $model,
        UploadedFile $file,
        string $collection = 'default',
        bool $singleFile = true,
        ?string $disk = 'public'
    ): void {
        $mediaAdder = $model->addMedia($file);

        if ($singleFile) {
            $model->clearMediaCollection($collection);
            $mediaAdder->toMediaCollection($collection, $disk);
        } else {
            $mediaAdder->addToMediaCollection($collection, $disk);
        }
    }
}
