<?php

namespace App\DTO\User;

use Spa<PERSON>\LaravelData\Attributes\Validation\Nullable;
use Spatie\LaravelData\Attributes\Validation\Required;
use Spatie\LaravelData\Data;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class UpdateProfileData extends Data
{
    public function __construct(
        #[Required]
        public string $name,
        #[Nullable]
        public ?string $email,
        #[Required]
        public string $country_code,
        #[Required]
        public string $phone,
        #[Nullable]
        public ?UploadedFile $image,
    ) {}
}
