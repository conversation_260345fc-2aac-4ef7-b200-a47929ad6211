<?php

use App\Enums\ApprovalStatus;
use App\Models\Supplier;

test('to array', function () {
    $rentalOutlet = Supplier::factory()->create()->fresh();

    expect(array_keys($rentalOutlet->toArray()))->toBe([
        'id',
        'user_id',
        'type',
        'company_name',
        'company_email',
        'tax_number',
        'commercial_register',
        'approval_status',
        'created_at',
        'updated_at',
    ]);
});

test('approval status is instance of enum', function () {
    $rentalOutlet = Supplier::factory()->create()->fresh();

    expect($rentalOutlet->approval_status)->toBeInstanceOf(ApprovalStatus::class);
});
