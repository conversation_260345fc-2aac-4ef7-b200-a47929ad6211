<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class TransportionMethodBelongsToShippingSize implements ValidationRule
{
    public function __construct() {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Expect attribute like: shipping_options.N.size_methods.SIZE_ID.M
        $parts = explode('.', $attribute);

        $sizeId = null;
        foreach ($parts as $index => $part) {
            if ($part === 'size_methods' && isset($parts[$index + 1])) {
                $sizeId = $parts[$index + 1];
                break;
            }
        }

        if (! $sizeId || ! is_numeric($sizeId) || ! is_numeric($value)) {
            $fail(__('The selected transportation method is invalid for the chosen shipping size.'));

            return;
        }

        $exists = DB::table('shipping_size_transportion_method')
            ->where('shipping_size_id', (int) $sizeId)
            ->where('transportion_method_id', (int) $value)
            ->exists();

        if (! $exists) {
            $fail(__('The selected transportation method is invalid for the chosen shipping size.'));
        }
    }
}
