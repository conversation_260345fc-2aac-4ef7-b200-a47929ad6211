<?php

use App\Enums\ApprovalStatus;
use App\Models\Technician;

test('to array', function () {
    $rentalOutlet = Technician::factory()->create()->fresh();

    expect(array_keys($rentalOutlet->toArray()))->toBe([
        'id',
        'user_id',
        'id_number',
        'nationality_id',
        'approval_status',
        'created_at',
        'updated_at',
    ]);
});

test('approval status is instance of enum', function () {
    $rentalOutlet = Technician::factory()->create()->fresh();

    expect($rentalOutlet->approval_status)->toBeInstanceOf(ApprovalStatus::class);
});
