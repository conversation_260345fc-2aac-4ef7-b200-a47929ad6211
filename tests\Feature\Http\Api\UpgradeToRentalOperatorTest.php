<?php

use App\Enums\ApprovalStatus;
use App\Models\RentalOutlet;
use App\Models\User;
use Database\Seeders\WorldTableSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use MatanYadaev\EloquentSpatial\Objects\Point;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->baseUrl = '/api/user/profile/upgrade-to-rental-operator';
    $this->user = makeUser(); // default user for validation tests etc.
    $this->seed(WorldTableSeeder::class);
});

/**
 * Helper to create a verified user with optional overrides.
 */
function makeUser(array $overrides = []): User
{
    return User::factory()
        ->verified()
        ->create($overrides)
        ->fresh();
}

test('user can upgrade to rental operator with complete data', function () {
    $user = makeUser([
        'email' => null,
        'bank_account_number' => null,
        'bank_name' => null,
        'iban' => null,
        'location' => null,
    ]);

    $requestData = [
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
    ];

    $response = $this->actingAs($user)->postJson($this->baseUrl, $requestData);

    $response->assertSuccessful()->assertJsonStructure(['data']);

    $user->refresh();
    expect($user->email)->toBe('<EMAIL>')
        ->and($user->bank_account_number)->toBe('**********')
        ->and($user->bank_name)->toBe('Test Bank')
        ->and($user->iban)->toBe('SA********************12')
        ->and($user->location->latitude)->toBe(24.7136)
        ->and($user->location->longitude)->toBe(46.6753);

    expect($user->pendingRentalOutlet)->toBeInstanceOf(RentalOutlet::class)
        ->and($user->pendingRentalOutlet->approval_status)->toBe(ApprovalStatus::PENDING);
});

test('user can upgrade to rental operator with partial data', function () {
    $user = makeUser([
        'email' => '<EMAIL>',
        'bank_account_number' => 'existing123',
        'location' => new Point(30.0444, 31.2357),
    ]);

    $requestData = [
        'bank_name' => 'New Bank',
        'iban' => 'EG********************12',
    ];

    $response = $this->actingAs($user)->postJson($this->baseUrl, $requestData);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->email)->toBe('<EMAIL>')
        ->and($user->bank_account_number)->toBe('existing123')
        ->and($user->bank_name)->toBe('New Bank')
        ->and($user->iban)->toBe('EG********************12')
        ->and($user->location->latitude)->toBe(30.0444)
        ->and($user->location->longitude)->toBe(31.2357);

    expect($user->pendingRentalOutlet)->toBeInstanceOf(RentalOutlet::class);
});

test('user cannot upgrade if they already have an approved rental outlet', function () {
    $user = makeUser([
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => new Point(24.7136, 46.6753),
    ]);

    RentalOutlet::factory()->approved()->for($user)->create();

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
    ]);

    $response->assertStatus(400)
        ->assertJson([
            'data' => 'You already have an approved rental operator upgrade request.',
        ]);
});

test('user cannot upgrade if they already have a pending rental outlet', function () {
    $user = makeUser([
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => new Point(24.7136, 46.6753),
    ]);

    RentalOutlet::factory()->pending()->for($user)->create();

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
    ]);

    $response->assertStatus(400)
        ->assertJson([
            'data' => 'You already have a pending rental operator upgrade request.',
        ]);
});

test('validation fails when required fields are missing', function () {
    $user = makeUser([
        'email' => null,
        'bank_account_number' => null,
        'bank_name' => null,
        'iban' => null,
        'location' => null,
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, []);

    $response->assertStatus(422)
        ->assertJsonValidationErrors([
            'email',
            'bank_account_number',
            'bank_name',
            'iban',
            'location',
        ]);
});

test('validation fails for invalid email format', function () {
    $user = makeUser(['email' => null]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => 'invalid-email',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['email']);
});

test('validation fails for duplicate email', function () {
    $user = makeUser(['email' => null]);
    makeUser(['email' => '<EMAIL>']);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>', // duplicate
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['email']);
});

test('validation fails for duplicate bank account number', function () {
    makeUser(['bank_account_number' => '**********']);

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'bank_account_number' => '**********', // duplicate
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['bank_account_number']);
});

test('validation fails for duplicate iban', function () {
    makeUser(['iban' => 'SA********************12']);

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12', // duplicate
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['iban']);
});

test('validation fails for invalid location coordinates', function () {
    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => ['lat' => 91, 'lng' => 46.6753],
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['location.lat']);
});

test('validation fails for location outside allowed countries', function () {
    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => ['lat' => 40.7128, 'lng' => -74.0060],
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['location']);
});

test('user with existing location does not need to provide location again', function () {
    $user = makeUser([
        'location' => new Point(24.7136, 46.6753),
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        // no location provided
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->location->latitude)->toBe(24.7136)
        ->and($user->location->longitude)->toBe(46.6753);
});

test('user with existing email does not need to provide email again', function () {
    $user = makeUser([
        'email' => '<EMAIL>',
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => 'SA********************12',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->email)->toBe('<EMAIL>');
});

test('user with existing bank details does not need to provide them again', function () {
    $user = makeUser([
        'bank_account_number' => '**********',
        'bank_name' => 'Existing Bank',
        'iban' => 'SA********************12',
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->bank_account_number)->toBe('**********')
        ->and($user->bank_name)->toBe('Existing Bank')
        ->and($user->iban)->toBe('SA********************12');
});
