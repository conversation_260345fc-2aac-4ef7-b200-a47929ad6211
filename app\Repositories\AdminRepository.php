<?php

namespace App\Repositories;

use App\Models\Admin;

class AdminRepository
{
    public function __construct(private readonly Admin $model) {}

    public function getById(string $id)
    {
        return $this->model->find($id);
    }

    public function getByEmailOrUsername(string $value)
    {
        return $this->model->where('email', $value)->orWhere('username', $value)->first();
    }

    public function getByEmail(string $email)
    {
        return $this->model->where('email', $email)->first();
    }

    public function getByPhone(string $phone)
    {
        return $this->model->whereIn('phone', [$phone, "0$phone", substr($phone, 1)])->first();
    }

    public function create(array $data)
    {
        return $this->model->create([
            'name' => $data['name'],
            'country_code' => $data['country_code'],
            'phone' => $data['phone'],
            'email' => $data['email'] ?? null,
            'password' => $data['password'],
            'role_id' => $data['role_id'],
        ]);
    }

    public function update(Admin $admin, array $data): void
    {
        $admin->update($data);
    }

    public function updatePassword(Admin $admin, string $password): void
    {
        $admin->update([
            'password' => bcrypt($password),
        ]);
    }

    public function invalidateUniqueData(Admin $admin): void
    {
        $admin->update([
            'phone' => getInvalidatedValue($admin->phone),
            'email' => getInvalidatedValue($admin->email),
            'username' => getInvalidatedValue($admin->username),
        ]);
    }

    public function findByPhoneWithCountryCode(string $phone, string $countryCode): ?User
    {
        return $this->model->where('phone', ltrim($phone, '0'))
            ->where('country_code', $countryCode)
            ->first();
    }
}
