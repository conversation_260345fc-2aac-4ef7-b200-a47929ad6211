<?php

namespace App\Http\Requests\Admin\GroupNotification;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title.en' => 'required|string|min:3|max:255',
            'title.ar' => 'required|string|min:3|max:255',
            'title.ur' => 'required|string|min:3|max:255',
            'body.en' => 'required|string|min:3|max:500',
            'body.ar' => 'required|string|min:3|max:500',
            'body.ur' => 'required|string|min:3|max:500',
            'type' => 'required|in:All,Users,Drivers,Admins,Company',
            'receivers' => 'required|array',
            'receivers.*' => 'integer',
        ];
    }
}
