name: run-tests

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master, develop ]

jobs:
  test:
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: true
      matrix:
        os: [ ubuntu-latest ]
        php: [ 8.2,8.3 ]
        laravel: [ 11.* ]
        stability: [  prefer-stable ]
        include:
          - laravel: 11.*
            testbench: 9.*
            carbon: ^2.63

    name: P${{ matrix.php }} - L${{ matrix.laravel }} - ${{ matrix.stability }} - ${{ matrix.os }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v5


      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          extensions: dom, curl, libxml, mbstring, zip, pcntl, pdo, sqlite, pdo_sqlite, bcmath, soap, intl, gd, exif, iconv, imagick, fileinfo, rdkafka, xdebug
          coverage: none

      - name: Setup problem matchers
        run: |
          echo "::add-matcher::${{ runner.tool_cache }}/php.json"
          echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Install dependencies
        run: |
          composer require "laravel/framework:${{ matrix.laravel }}" "orchestra/testbench:${{ matrix.testbench }}" "nesbot/carbon:${{ matrix.carbon }}" --no-interaction --no-update
          composer update --${{ matrix.stability }} --prefer-dist --no-interaction

      - name: List Installed Dependencies
        run: composer show -D

      - name: Prepare Laravel Application
        run: |
          sudo /etc/init.d/mysql start
          mysql -e "CREATE DATABASE IF NOT EXISTS ms_core;" -uroot -proot
          cp .env.example .env
          php artisan key:generate

      - name: Execute tests
        run: vendor/bin/pest --ci
