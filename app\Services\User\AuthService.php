<?php

namespace App\Services\User;

use App\DTO\User\RegisterData;
use App\Repositories\UserRepository;
use App\Traits\HasOtp;
use Illuminate\Support\Facades\DB;

class AuthService
{
    use HasOtp;

    public function __construct(private readonly UserRepository $userRepository)
    {
        //
    }

    public function register(RegisterData $dto): string
    {
        return DB::transaction(function () use ($dto): string {
            $data = $dto->toArray();
            $data['phone'] = normalizePhoneNumber($dto->phone);

            $user = $this->userRepository->create($data);

            $this->generateAndSendOtp($user, 'phone', $dto->phone, $dto->country_code);

            return __('Your account has been successfully registered, please verify your account');
        });
    }

    public function resetPassword(string $password): string
    {
        $user = auth()->user();

        $user->update([
            'password' => $password,
        ]);

        $user->currentAccessToken()->delete();

        return __('Password has been changed successfully.');
    }

    public function logout(): string
    {
        auth()->user()->currentAccessToken()->delete();

        return __('Logged out successfully.');
    }
}
