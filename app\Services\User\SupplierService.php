<?php

namespace App\Services\User;

use App\Models\User;
use App\Repositories\SupplierRepository;
use Illuminate\Validation\ValidationException;
use App\DTO\Supplier\UpgradeSupplierData;

class SupplierService
{
    public function __construct(
        protected SupplierRepository $supplierRepository
    ) {}

    public function upgradeUserToSupplier(User $user, UpgradeSupplierData $dto)
    {
        if ($this->supplierRepository->findByUserId($user->id)) {
            throw ValidationException::withMessages([
                'supplier' => 'This user is already a supplier.'
            ]);
        }
        return $this->supplierRepository->create($dto->toSupplierArray($user->id), $dto->categories);
    }
}
