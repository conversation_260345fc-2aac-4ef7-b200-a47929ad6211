<?php

namespace App\Services\User;

use App\DTO\Supplier\UpgradeSupplierData;
use App\Models\Supplier;
use App\Models\User;
use App\Repositories\SupplierRepository;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class SupplierService
{
    public function __construct(
        private readonly SupplierRepository $supplierRepository,
        private readonly UserRepository $userRepository
    ) {}

    public function upgradeUserToSupplier(User $user, UpgradeSupplierData $dto)
    {
        if ($this->supplierRepository->findByUserId($user->id)) {
            throw ValidationException::withMessages([
                'supplier' => 'This user is already a supplier.',
            ]);
        }

        return $this->supplierRepository->create($dto->toSupplierArray($user->id), $dto->categories);
    }

    public function create(UpgradeSupplierData $dto): Supplier
    {
        $user = auth('user')->user();

        if ($this->userRepository->hasPendingRequest($user)) {
            throw new BadRequestHttpException(__('You already have a pending upgrade request.'));
        }

        if ($user->supplier()->exists()) {
            throw new BadRequestHttpException(__('You already have an approved supplier upgrade request.'));
        }

        return DB::transaction(function () use ($user, $dto): Supplier {
            if (! empty($dto->userData())) {
                $this->userRepository->updateData($user, $dto->userData());
            }

            return $this->supplierRepository->create($dto->supplierData());
        });
    }
}
