<?php

use App\Enums\ApprovalStatus;
use App\Models\Category;
use App\Models\Supplier;
use Database\Seeders\CategorySeeder;
use Database\Seeders\WorldTableSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use MatanYadaev\EloquentSpatial\Objects\Point;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->baseUrl = '/api/user/profile/upgrade-to-supplier';
    $this->user = $this->createUser(); // default user for validation tests etc.
    $this->seed(WorldTableSeeder::class);
    $this->seed(CategorySeeder::class);
});

test('user can upgrade to supplier with complete data', function () {
    Storage::fake('public');

    $categories = Category::take(2)->pluck('id')->toArray();

    $user = $this->createUser([
        'username' => null,
        'email' => null,
        'bank_account_number' => null,
        'bank_name' => null,
        'iban' => null,
        'location' => null,
    ]);

    $requestData = [
        'username' => 'testuser123',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company Ltd',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
        'company_logo' => UploadedFile::fake()->image('logo.jpg'),
    ];

    $response = $this->actingAs($user)->postJson($this->baseUrl, $requestData);

    $response->assertSuccessful()->assertJsonStructure(['data']);

    $user->refresh();
    expect($user->username)->toBe('testuser123')
        ->and($user->email)->toBe('<EMAIL>')
        ->and($user->bank_account_number)->toBe('**********')
        ->and($user->bank_name)->toBe('Test Bank')
        ->and($user->iban)->toBe('************************')
        ->and($user->location->latitude)->toBe(24.7136)
        ->and($user->location->longitude)->toBe(46.6753);

    expect($user->pendingSupplier)->toBeInstanceOf(Supplier::class)
        ->and($user->pendingSupplier->approval_status)->toBe(ApprovalStatus::PENDING)
        ->and($user->pendingSupplier->type)->toBe('wholesale')
        ->and($user->pendingSupplier->company_name)->toBe('Test Company Ltd')
        ->and($user->pendingSupplier->company_email)->toBe('<EMAIL>')
        ->and($user->pendingSupplier->tax_number)->toBe('***************')
        ->and($user->pendingSupplier->commercial_register)->toBe('**********');

    expect($user->pendingSupplier->categories)->toHaveCount(2);
    expect($user->pendingSupplier->company_logo)->not->toBeNull();
});

test('user can upgrade to supplier with partial data', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $user = $this->createUser([
        'username' => 'existinguser',
        'email' => '<EMAIL>',
        'bank_account_number' => 'existing123',
        'location' => new Point(30.0444, 31.2357),
    ]);

    $requestData = [
        'bank_name' => 'New Bank',
        'iban' => '************************',
        'type' => 'retail',
        'company_name' => 'Partial Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ];

    $response = $this->actingAs($user)->postJson($this->baseUrl, $requestData);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->username)->toBe('existinguser')
        ->and($user->email)->toBe('<EMAIL>')
        ->and($user->bank_account_number)->toBe('existing123')
        ->and($user->bank_name)->toBe('New Bank')
        ->and($user->iban)->toBe('************************')
        ->and($user->location->latitude)->toBe(30.0444)
        ->and($user->location->longitude)->toBe(31.2357);

    expect($user->pendingSupplier)->toBeInstanceOf(Supplier::class)
        ->and($user->pendingSupplier->type)->toBe('retail')
        ->and($user->pendingSupplier->company_name)->toBe('Partial Company');
});

test('user cannot upgrade if they already have an approved supplier', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $user = $this->createUser([
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => new Point(24.7136, 46.6753),
    ]);

    Supplier::factory()->approved()->for($user)->create();

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(400)
        ->assertJson([
            'data' => 'You already have an approved supplier upgrade request.',
        ]);
});

test('user cannot upgrade if they already have a pending supplier', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $user = $this->createUser([
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => new Point(24.7136, 46.6753),
    ]);

    Supplier::factory()->pending()->for($user)->create();

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(400)
        ->assertJson([
            'data' => 'You already have a pending upgrade request.',
        ]);
});

test('validation fails when required fields are missing', function () {
    $user = $this->createUser([
        'username' => null,
        'email' => null,
        'bank_account_number' => null,
        'bank_name' => null,
        'iban' => null,
        'location' => null,
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, []);

    $response->assertStatus(422)
        ->assertJsonValidationErrors([
            'username',
            'email',
            'bank_account_number',
            'bank_name',
            'iban',
            'location',
            'type',
            'company_name',
            'company_email',
            'tax_number',
            'commercial_register',
            'categories',
        ]);
});

test('validation fails for invalid email format', function () {
    $categories = Category::take(1)->pluck('id')->toArray();
    $user = $this->createUser(['email' => null]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => 'invalid-email',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['email']);
});

test('validation fails for duplicate email', function () {
    $categories = Category::take(1)->pluck('id')->toArray();
    $user = $this->createUser(['email' => null]);
    $this->createUser(['email' => '<EMAIL>']);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>', // duplicate
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['email']);
});

test('validation fails for duplicate username', function () {
    $categories = Category::take(1)->pluck('id')->toArray();
    $user = $this->createUser(['username' => null]);
    $this->createUser(['username' => 'existinguser']);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'username' => 'existinguser', // duplicate
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['username']);
});

test('validation fails for duplicate bank account number', function () {
    $categories = Category::take(1)->pluck('id')->toArray();
    $this->createUser(['bank_account_number' => '**********']);

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********', // duplicate
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['bank_account_number']);
});

test('validation fails for duplicate iban', function () {
    $categories = Category::take(1)->pluck('id')->toArray();
    $this->createUser(['iban' => '************************']);

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************', // duplicate
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['iban']);
});

test('validation fails for duplicate company email', function () {
    $categories = Category::take(1)->pluck('id')->toArray();
    $existingUser = $this->createUser();
    Supplier::factory()->for($existingUser)->create(['company_email' => '<EMAIL>']);

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>', // duplicate
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['company_email']);
});

test('validation fails for duplicate tax number', function () {
    $categories = Category::take(1)->pluck('id')->toArray();
    $existingUser = $this->createUser();
    Supplier::factory()->for($existingUser)->create(['tax_number' => '***************']);

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************', // duplicate
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['tax_number']);
});

test('validation fails for duplicate commercial register', function () {
    $categories = Category::take(1)->pluck('id')->toArray();
    $existingUser = $this->createUser();
    Supplier::factory()->for($existingUser)->create(['commercial_register' => '**********']);

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********', // duplicate
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['commercial_register']);
});

test('validation fails for invalid supplier type', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'invalid_type',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['type']);
});

test('validation fails for invalid tax number format', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '12345', // invalid format (should be 15 digits)
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['tax_number']);
});

test('validation fails for invalid commercial register format', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '123', // invalid format (should be 10 digits)
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['commercial_register']);
});

test('validation fails for invalid categories', function () {
    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => [99999], // non-existent category
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['categories']);
});

test('validation fails for empty categories', function () {
    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => [], // empty categories
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['categories']);
});

test('validation fails for invalid location coordinates', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 91, 'lng' => 46.6753], // invalid latitude
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['location.lat']);
});

test('validation fails for location outside allowed countries', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 40.7128, 'lng' => -74.0060], // New York (outside SA/EG)
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['location']);
});

test('validation fails for invalid company logo file', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $response = $this->actingAs($this->user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
        'company_logo' => UploadedFile::fake()->create('document.pdf', 1000), // invalid file type
    ]);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['company_logo']);
});

test('user with existing location does not need to provide location again', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $user = $this->createUser([
        'location' => new Point(24.7136, 46.6753),
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
        // no location provided
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->location->latitude)->toBe(24.7136)
        ->and($user->location->longitude)->toBe(46.6753);
});

test('user with existing email does not need to provide email again', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $user = $this->createUser([
        'email' => '<EMAIL>',
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
        // no email provided
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->email)->toBe('<EMAIL>');
});

test('user with existing username does not need to provide username again', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $user = $this->createUser([
        'username' => 'existinguser',
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'email' => '<EMAIL>',
        'bank_account_number' => '**********',
        'bank_name' => 'Test Bank',
        'iban' => '************************',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
        // no username provided
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->username)->toBe('existinguser');
});

test('user with existing bank details does not need to provide them again', function () {
    $categories = Category::take(1)->pluck('id')->toArray();

    $user = $this->createUser([
        'bank_account_number' => '**********',
        'bank_name' => 'Existing Bank',
        'iban' => '************************',
    ]);

    $response = $this->actingAs($user)->postJson($this->baseUrl, [
        'username' => 'testuser',
        'email' => '<EMAIL>',
        'location' => ['lat' => 24.7136, 'lng' => 46.6753],
        'type' => 'wholesale',
        'company_name' => 'Test Company',
        'company_email' => '<EMAIL>',
        'tax_number' => '***************',
        'commercial_register' => '**********',
        'categories' => $categories,
        // no bank details provided
    ]);

    $response->assertSuccessful();

    $user->refresh();
    expect($user->bank_account_number)->toBe('**********')
        ->and($user->bank_name)->toBe('Existing Bank')
        ->and($user->iban)->toBe('************************');
});
