<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckStatus
{
    public function handle(Request $request, Closure $next): Response
    {
        $guard = request()->routeIs('admin.*') ? 'admin' : 'provider';

        if (! auth($guard)->user()->is_active) {
            auth($guard)->logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route("$guard.login");
        }

        return $next($request);
    }
}
