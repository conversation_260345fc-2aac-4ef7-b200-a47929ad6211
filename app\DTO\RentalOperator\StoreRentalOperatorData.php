<?php

namespace App\DTO\RentalOperator;

use App\Enums\ApprovalStatus;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Spatie\LaravelData\Data;

class StoreRentalOperatorData extends Data
{
    public function __construct(
        public ?string $email,
        public ?string $bank_account_number,
        public ?string $bank_name,
        public ?string $iban,
        public ?array $location,
    ) {
        //
    }

    /**
     * Extract fields that belong to the User model.
     */
    public function userData(): array
    {
        $data = array_filter([
            'email' => $this->email,
            'bank_account_number' => $this->bank_account_number,
            'bank_name' => $this->bank_name,
            'iban' => $this->iban,
        ], fn ($value) => ! is_null($value));

        if ($this->location) {
            $data['location'] = new Point(
                $this->location['lat'],
                $this->location['lng']
            );
        }

        return $data;
    }

    /**
     * Extract fields that belong to RentalOperator model.
     */
    public function rentalOperatorData(): array
    {
        return [
            'user_id' => auth('user')->id(),
            'approval_status' => ApprovalStatus::PENDING,
        ];
    }
}
