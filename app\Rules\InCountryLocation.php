<?php

namespace App\Rules;

use App\Models\Country;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class InCountryLocation implements ValidationRule
{
    private array $isoCodes;

    private int $maxRadiusKm;

    /**
     * @param  array|string  $isoCodes  One or more ISO country codes (e.g., 'SA' or ['SA','AE'])
     */
    public function __construct(array|string $isoCodes, int $maxRadiusKm = 800)
    {
        $this->isoCodes = (array) $isoCodes;
        $this->maxRadiusKm = $maxRadiusKm;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_array($value) || ! isset($value['lat'], $value['lng'])) {
            $fail(__('validation.custom.location.latlng_required', ['attribute' => __("validation.attributes.{$attribute}")]));

            return;
        }

        $lat = (float) $value['lat'];
        $lng = (float) $value['lng'];

        // Fetch allowed countries from DB
        $countries = Country::whereIn('iso2', array_map('strtoupper', $this->isoCodes))->get();

        if ($countries->isEmpty()) {
            $fail(__('Configured countries are not available.'));

            return;
        }

        // Check if location is inside any of the allowed countries
        foreach ($countries as $country) {
            $distance = $this->haversineDistance(
                $lat,
                $lng,
                (float) $country->latitude,
                (float) $country->longitude
            );

            if ($distance <= $this->maxRadiusKm) {
                return;
            }
        }

        $countryNames = $countries->pluck('translated_name')->join(', ');
        $fail(__('validation.custom.location.location_in_countries', ['attribute' => __("validation.attributes.{$attribute}"), 'countries' => $countryNames]));
    }

    private function haversineDistance($lat1, $lng1, $lat2, $lng2): float
    {
        $earthRadius = 6371; // km

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLng / 2) * sin($dLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }
}
