<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\ContactDataTable;
use App\Http\Controllers\Controller;
use App\Services\ContactService;

class ContactController extends Controller
{
    protected $contactService;

    public function __construct(ContactService $contactService)
    {
        $this->contactService = $contactService;
    }

    /**
     * Display a listing of the contact messages.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(ContactDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.contacts.index');
    }

    /**
     * Display the specified contact message.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $contact = $this->contactService->findById($id);

        if (! $contact) {
            return redirect()->route('admin.contacts.index')->with('error', 'Contact not found.');
        }

        return view('pages.admin.contacts.show', compact('contact'));
    }

    /**
     * Remove the specified contact message from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $result = $this->contactService->delete($id);

        if ($result) {
            return redirect()->route('admin.contacts.index')->with('success', 'Contact deleted successfully.');
        } else {
            return redirect()->route('admin.contacts.index')->with('error', 'Failed to delete contact.');
        }
    }
}
