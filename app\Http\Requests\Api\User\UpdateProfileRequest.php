<?php

namespace App\Http\Requests\Api\User;

use App\Rules\ValidMedia;
use App\Rules\ValidPhone;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:3', 'max:30'],
            'email' => ['nullable', 'email:dns,rfc', 'unique:users,email,'.auth()->id()],
            'country_code' => ['required_with:phone', 'required', 'regex:/^\d{1,4}$/'],
            'phone' => [
                'required_with:country_code',
                'required',
                Rule::unique('users')
                    ->where(
                        fn ($query) => $query
                            ->where('country_code', ltrim($this->country_code, '+'))
                            ->where('phone', ltrim($this->phone, '0'))
                    )->ignore(auth()->id()),
                new ValidPhone($this->input('country_code')),
            ],
            'image' => ['nullable', 'image', new ValidMedia(['image'])],
        ];
    }
}
