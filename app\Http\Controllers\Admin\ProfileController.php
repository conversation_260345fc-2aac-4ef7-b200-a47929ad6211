<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ProfileRequest;
use App\Services\Admin\ProfileService;

class ProfileController extends Controller
{
    public function __construct(private ProfileService $profileService) {}

    public function index()
    {
        $admin = auth('admin')->user();

        return view('pages.admin.profile.index', compact('admin'));
    }

    public function update(ProfileRequest $request)
    {
        $this->profileService->update($request->validated());

        return back()->with('success', __('Profile updated successfully'));
    }
}
