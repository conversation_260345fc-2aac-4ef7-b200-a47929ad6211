<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ProfileRequest;
use App\Repositories\CountryRepository;
use App\Services\Admin\ProfileService;

class ProfileController extends Controller
{
    public function __construct(
        private readonly CountryRepository $countryRepository,
        private readonly ProfileService $profileService
    ) {}

    public function index()
    {
        $countries = $this->countryRepository->getAll();

        return view('pages.admin.profile.index', compact('countries'));
    }

    public function update(ProfileRequest $request)
    {
        $this->profileService->updateProfile();

        return back()->with('success', __('Profile updated successfully.'));
    }
}
