<?php

namespace App\Services\General;

use App\Models\Country;
use App\Models\State;
use App\Repositories\CityRepository;
use App\Repositories\CountryRepository;
use App\Repositories\StateRepository;

class WorldService
{
    public function __construct(
        private readonly CountryRepository $countryRepo,
        private readonly StateRepository $stateRepo,
        private readonly CityRepository $cityRepo
    ) {
        //
    }

    public function getCountries()
    {
        return $this->countryRepo->getForSelect();
    }

    public function getStatesOfCountry(Country $country)
    {
        return $this->stateRepo->getForSelect($country->id);
    }

    public function getCitiesOfCountry(Country $country)
    {
        return $this->cityRepo->getByCountry($country->id);
    }

    public function getCitiesOfState(State $state)
    {
        return $this->cityRepo->getForSelect($state->id);
    }
}
