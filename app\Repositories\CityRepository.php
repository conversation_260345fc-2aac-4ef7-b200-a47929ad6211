<?php

namespace App\Repositories;

use App\Models\City;

class CityRepository
{
    public function __construct(private readonly City $model)
    {
        //
    }

    public function getForSelect(string $stateId)
    {
        return $this->model
            ->active()
            ->where('state_id', $stateId)
            ->select(['id', 'name'])
            ->get();
    }

    public function getByCountry(string $countryId)
    {
        return $this->model
            ->active()
            ->whereHas('state', function ($q) use ($countryId) {
                $q->active()
                    ->where('country_id', $countryId)
                    ->whereHas('country', fn ($query) => $query->active());
            })
            ->select(['id', 'name'])
            ->get();
    }
}
