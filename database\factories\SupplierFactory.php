<?php

namespace Database\Factories;

use App\Enums\ApprovalStatus;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Supplier>
 */
class SupplierFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::where('is_verified', true)->inRandomOrder()->value('id'),
            'type' => fake()->randomElement(['wholesale', 'retail']),
            'company_name' => fake()->company(),
            'company_email' => fake()->companyEmail(),
            'tax_number' => fake()->numberBetween(100000000000000, 999999999999999),
            'commercial_register' => fake()->numberBetween(1000000000, 9999999999),
            'approval_status' => fake()->randomElement(ApprovalStatus::cases()),
        ];
    }

    /**
     * Create a rental outlet with approved status.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => ApprovalStatus::APPROVED,
        ]);
    }

    /**
     * Create a rental outlet with pending status.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => ApprovalStatus::PENDING,
        ]);
    }

    /**
     * Create a rental outlet with rejected status.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'approval_status' => ApprovalStatus::REJECTED,
        ]);
    }
}
