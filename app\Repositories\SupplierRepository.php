<?php

namespace App\Repositories;

use App\Models\Supplier;

class SupplierRepository
{
    public function __construct(private readonly Supplier $model)
    {
        //
    }

    public function create(array $data): Supplier
    {
        $supplier = $this->model->create($data);

        if (isset($data['categories'])) {
            $supplier->categories()->sync($data['categories']);
        }

        if (isset($data['company_logo'])) {
            if ($supplier->company_logo) {
                $supplier->clearMediaCollection('company_logo');
            }
            $supplier->addMedia($data['company_logo'])->toMediaCollection('company_logo');
        }

        return $supplier;
    }

    public function findByUserId(int $userId): ?Supplier
    {
        return $this->model->where('user_id', $userId)->first();
    }
}
