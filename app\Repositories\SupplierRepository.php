<?php

namespace App\Repositories;

use App\Models\Supplier;

class SupplierRepository
{
    public function create(array $supplierData, array $categories): Supplier
    {
        $supplier = Supplier::create($supplierData);

        $this->syncCategories($supplier, $categories);

        return $supplier;
    }

    public function findByUserId(int $userId): ?Supplier
    {
        return Supplier::where('user_id', $userId)->first();
    }

    protected function syncCategories(Supplier $supplier, array $categories): void
    {
        $supplier->categories()->sync($categories);
    }
}
