<?php

namespace App\Services\Admin;

use App\Models\Admin;
use App\Repositories\AdminRepository;
use App\Services\MediaUploaderService;
use Illuminate\Support\Facades\DB;

class AdminService
{
    public function __construct(
        private readonly AdminRepository $adminRepository,
        private readonly MediaUploaderService $mediaService
    ) {}

    public function getAdmin(Admin $admin): Admin
    {
        if ($admin->id == 1 || $admin->id == auth('admin')->id()) {
            abort(404);
        }

        return $admin;
    }

    public function create(): void
    {
        $data = request()->all();

        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['password'] = bcrypt(request('password'));

        DB::transaction(function () use ($data): void {
            $admin = $this->adminRepository->create($data);

            if (request('image')) {
                $this->mediaService->upload($admin, request('image'), 'admins');
            }

            $admin->permissions()->attach(request('permissions'));
        });
    }

    public function update(Admin $admin): void
    {
        if ($admin->id == 1 || $admin->id == auth('admin')->id()) {
            abort(404);
        }

        $data = request()->all();

        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['status'] = request('status') ? 'active' : 'inactive';
        $data['password'] = request('password') ? bcrypt(request('password')) : $admin->password;

        DB::transaction(function () use ($admin, $data): void {
            $this->adminRepository->update($admin, $data);

            if (request('image')) {
                $this->mediaService->delete($admin->image);
                $this->mediaService->upload($admin, request('image'), 'admins');
            }

            $admin->permissions()->sync(request('permissions'));
        });
    }

    public function delete(Admin $admin): void
    {
        if ($admin->id == 1 || $admin->id == auth('admin')->id()) {
            abort(404);
        }

        DB::transaction(function () use ($admin): void {
            $admin->delete();
            $this->adminRepository->invalidateUniqueData($admin);
        });
    }

    public function updateProfile(): void
    {
        $admin = auth('admin')->user();
        $data = request()->all();

        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['password'] = request('password') ? bcrypt(request('password')) : $admin->password;

        DB::transaction(function () use ($admin, $data): void {
            $this->adminRepository->update($admin, $data);

            if (request('image')) {
                $this->mediaService->delete($admin->image);
                $this->mediaService->upload($admin, request('image'), 'admins');
            }
        });
    }
}
