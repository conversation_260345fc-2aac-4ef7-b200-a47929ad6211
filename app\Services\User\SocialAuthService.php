<?php

namespace App\Services\User;

use App\DTO\User\SocialLoginData;
use App\Http\Resources\User\AuthResource;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\DB;

class SocialAuthService
{
    public function __construct(
        private readonly UserRepository $userRepository
    ) {
        //
    }

    /**
     * Authenticate or register user with social credentials
     */
    public function login(SocialLoginData $data): array
    {
        return DB::transaction(function () use ($data): array {
            // Check if user exists with this email
            $user = $this->userRepository->findByEmail($data->email);

            if (! $user instanceof \App\Models\User) {
                // Create new user
                $user = $this->userRepository->create([
                    'name' => $data->name,
                    'email' => $data->email,
                    'email_verified_at' => now(), // Social login emails are pre-verified
                ]);

                // Create social credential
                $user->socialCredentials()->create([
                    'provider' => $data->provider,
                    'provider_id' => $data->provider_id,
                ]);
            } else {
                // Check if user has social credentials for this provider
                $socialCredential = $user->socialCredentials()
                    ->where('provider', $data->provider)
                    ->where('provider_id', $data->provider_id)
                    ->first();

                if (! $socialCredential) {
                    // Add new social credential to existing user
                    $user->socialCredentials()->create([
                        'provider' => $data->provider,
                        'provider_id' => $data->provider_id,
                    ]);
                }
            }

            // Update device token if provided
            if ($data->device_id !== null && $data->device_id !== '' && $data->device_id !== '0') {
                $user->updateDeviceToken([
                    'device_id' => $data->device_id,
                    'fb_token' => $data->device_id,
                ]);
            }

            return [
                'user' => new AuthResource($user),
                'token' => $user->createToken('social-token')->plainTextToken,
            ];
        });
    }
}
