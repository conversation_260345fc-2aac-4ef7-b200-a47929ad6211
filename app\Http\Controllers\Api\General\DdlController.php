<?php

namespace App\Http\Controllers\Api\General;

use App\Http\Controllers\Controller;
use App\Http\Resources\General\CityResource;
use App\Http\Resources\General\CountryResource;
use App\Http\Resources\General\StateResource;
use App\Models\Country;
use App\Models\State;
use App\Services\General\WorldService;

class DdlController extends Controller
{
    public function __construct(private readonly WorldService $worldService)
    {
        //
    }

    public function getCountries()
    {
        $countries = $this->worldService->getCountries();

        return success(CountryResource::collection($countries));
    }

    public function getStates(Country $country)
    {
        $states = $this->worldService->getStatesOfCountry($country);

        return success(StateResource::collection($states));
    }

    public function getCitiesOfCountry(Country $country)
    {
        $states = $this->worldService->getCitiesOfCountry($country);

        return success(CityResource::collection($states));
    }

    public function getCities(State $state)
    {
        $cities = $this->worldService->getCitiesOfState($state);

        return success(CityResource::collection($cities));
    }
}
