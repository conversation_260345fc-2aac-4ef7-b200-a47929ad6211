<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

// Broadcasting routes for authenticated channels
Broadcast::routes(['middleware' => ['web', 'auth:admin']]);
Route::post('/broadcasting/auth', function () {
    // Only authenticate if user is logged in as admin
    if (auth()->guard('admin')->check()) {
        return response()->json(['auth' => true]);
    }

    return response()->json(['auth' => false], 403);
})->middleware(['web', 'auth:admin'])->name('broadcasting.auth');

// Public broadcasting route for unauthenticated access
Route::post('/api/broadcasting/auth', function () {
    return response()->json(['auth' => true]);
})->middleware(['web'])->name('broadcasting.public.auth');

Route::get('/change-language/{lang}', [\App\Http\Controllers\LangController::class, 'changeLang'])->name('change-language');
Route::get('/privacy-policy', [\App\Http\Controllers\PageController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('/terms-and-conditions', [\App\Http\Controllers\PageController::class, 'termsAndConditions'])->name('terms-and-conditions');
Route::get('/delete-account', [\App\Http\Controllers\PageController::class, 'deleteAccount'])->name('delete-account');

Route::get('locale/{locale}', function ($locale, Request $request) {
    $locales = config('app.locales', ['en']);

    if (in_array($locale, $locales)) {
        // Save in session for web requests
        if ($request->hasSession()) {
            session(['locale' => $locale]);
        }

        // Set immediately so user sees effect without reload
        App::setLocale($locale);
    }

    return back();
})->name('locale.switch');

if (! App::environment('production')) {
    Route::get('clear-cache', function () {
        Artisan::call('cache:clear');

        return 'cache cleared';
    });
}
