<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Permission\Traits\HasRoles;

class Admin extends Authenticatable implements HasMedia
{
    use HasRoles, InteractsWithMedia, Notifiable;

    protected $fillable = [
        'name',
        'username',
        'email',
        'phone',
        'password',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'is_active' => 'boolean',
            'password' => 'hashed',
        ];
    }

    public function updateDeviceToken(array $data): void
    {
        $this->device_token()->updateOrCreate(
            ['device_id' => $data['device_id']],
            [
                'device_id' => $data['device_id'],
                'firebase_token' => $data['firebase_token'],
            ]
        );
    }

    public function otps(): MorphMany
    {
        return $this->morphMany(Otp::class, 'otpeable');
    }

    public function resetCodes(): MorphMany
    {
        return $this->morphMany(PasswordResetCode::class, 'user');
    }

    public function device_token(): MorphOne
    {
        return $this->morphOne(DeviceToken::class, 'userable');
    }

    public function getImageAttribute()
    {
        return $this->getMedia('*')->first();
    }

    public function getImageUrlAttribute()
    {
        return $this->image ? $this->image->getUrl() : asset('user-default.png');
    }
}
