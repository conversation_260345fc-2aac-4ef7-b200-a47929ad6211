<?php

namespace App\Http\Controllers\Api\User;

use App\DTO\Technician\StoreTechnicianData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\Technician\StoreRequest;
use App\Services\User\TechnicianService;

class TechnicianController extends Controller
{
    public function __construct(private readonly TechnicianService $technicianService)
    {
        //
    }

    public function store(StoreRequest $request)
    {
        $dto = StoreTechnicianData::from($request->validated());
        $this->technicianService->create($dto);

        return success(__('Your account upgrade request has been sent successfully.'));
    }
}
