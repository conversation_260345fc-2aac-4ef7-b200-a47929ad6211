<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $admin = Admin::create([
            'name' => 'Admin 1',
            'email' => '<EMAIL>',
            'country_code' => '+966',
            'phone' => '555555555',
            'password' => Hash::make('Benaa5000!'),
        ]);

        $role = Role::where('name', 'super admin')->first();
        $admin->assignRole($role);
    }
}
