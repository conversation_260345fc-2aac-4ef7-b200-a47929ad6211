<?php

namespace App\DataTables\Admin;

use App\Models\Contact;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class ContactDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->setRowId('id')
            ->addColumn('actions', 'pages.admin.contacts.actions')
            ->addColumn('phone', fn ($contact): string => formatPhone($contact->country_code, $contact->phone))
            ->addColumn('email', fn ($contact) => $contact->email ?? '-')
            ->addColumn('created_at', fn ($contact): ?string => formatDateTime($contact->created_at))
            ->addColumn('message', fn ($contact): string => \Str::limit($contact->message, 50))
            ->orderColumn('DT_RowIndex', function ($query, $direction): void {
                $query->orderBy('id', $direction);
            })
            ->rawColumns(['actions', 'phone'])
            ->addIndexColumn();
    }

    public function query(Contact $model): QueryBuilder
    {
        return $model->newQuery()
            ->when(request('search_param'), function ($query): void {
                $query->where('name', 'LIKE', '%'.request('search_param').'%')
                    ->orWhere('email', 'LIKE', '%'.request('search_param').'%')
                    ->orWhereRaw('CONCAT(country_code, phone) LIKE ?', ['%'.request('search_param').'%']);
            })
            ->when(request('from_date'), fn ($q) => $q->whereDate('created_at', '>=', request('from_date')))
            ->when(request('to_date'), fn ($q) => $q->whereDate('created_at', '<=', request('to_date')));
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('contacts-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'stateSave' => true,
                'stateDuration' => 10,
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::make('name')->title(__('Name'))->addClass('text-center')->orderable(false),
            Column::computed('email')->title(__('Email'))->addClass('text-center'),
            Column::computed('phone')->title(__('Phone'))->addClass('text-center'),
            Column::computed('message')->title(__('Message'))->addClass('text-center'),
            Column::computed('created_at')->title(__('Created At'))->addClass('text-center'),
            Column::computed('actions')->title(__('Actions'))->addClass('text-center'),

        ];
    }

    protected function filename(): string
    {
        return 'Contacts_'.date('YmdHis');
    }
}
