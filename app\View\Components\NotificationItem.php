<?php

namespace App\View\Components;

use App\Models\Order;
use Illuminate\Support\Str;
use Illuminate\View\Component;

class NotificationItem extends Component
{
    public $notification;

    public $guard;

    public $full;

    public function __construct($notification, $guard, $full = false)
    {
        $this->notification = $notification;
        $this->guard = $guard;
        $this->full = $full;
    }

    /**
     * Return final URL for the notification
     */
    public function getNotificationRoute(): string
    {
        // normalize type
        $type = $this->notification->data['type']
            ?? Str::afterLast($this->notification->type, '\\');

        $notifId = $this->notification->id;

        return match ($type) {
            // company registered -> go to company show (if id exists)
            'new_registered_company' => $this->companyRoute($notifId),

            // order notifications -> use order logic
            'order_delivered_to_hub',
            'order_forwarded_to_hub' => $this->getOrderRoute($notifId),

            // group notification -> no redirect
            'group_notification' => '#',

            // fallback -> try order logic
            default => '#',
        };
    }

    private function companyRoute(string|int $notifId): string
    {
        $companyId = $this->notification->data['company_id'] ?? null;
        if (! $companyId) {
            return '#';
        }

        return route('admin.company-registration-requests.show', [
            'company_registration_request' => $companyId,
            'notification' => $notifId,
        ]);
    }

    private function getOrderRoute(string|int $notifId): string
    {
        $orderId = $this->notification->data['order_id'] ?? null;

        if (! $orderId) {
            return $this->getOrdersIndexRoute();
        }

        $order = Order::find($orderId);
        if (! $order) {
            return $this->getOrdersIndexRoute();
        }

        $user = auth($this->guard)->user();

        if ($user?->is_hub_admin) {
            $orderStatus = $order->status?->value;

            return match ($orderStatus) {
                'delivered_to_local_hub' => route('company.intercity-hub-orders-forwarding.show', [
                    'order_id' => $orderId,
                    'notification' => $notifId,
                ]),
                'delivered_to_second_hub' => route('company.hub-order-items-forwarding.show', [
                    'order_id' => $orderId,
                    'notification' => $notifId,
                ]),
                default => $this->getOrdersIndexRoute(),
            };
        }

        $prefix = $this->getOrdersPrefix();

        return route($prefix.'.show', [
            'order' => $orderId,
            'notification' => $notifId,
        ]);
    }

    private function getOrdersIndexRoute(): string
    {
        $prefix = $this->getOrdersPrefix();

        return route($prefix.'.index', ['notification' => $this->notification->id]);
    }

    private function getOrdersPrefix(): string
    {
        return match ($this->guard) {
            'admin' => 'admin.orders',
            'company' => 'company.orders',
            default => 'company.orders',
        };
    }

    public function getTitle(): string
    {
        $locale = app()->getLocale();
        $title = $this->notification->data["title_{$locale}"]
            ?? $this->notification->data['title']
            ?? '';

        if (is_array($title)) {
            $title = implode(' ', array_map('strval', $title));
        }

        return Str::limit((string) $title, 50);
    }

    public function getBody(bool $full = false): string
    {
        $locale = app()->getLocale();
        $body = $this->notification->data["body_{$locale}"]
            ?? $this->notification->data['body']
            ?? '';

        if (is_array($body)) {
            $body = implode(' ', array_map('strval', $body));
        }

        return $full ? (string) $body : Str::limit((string) $body, 100);
    }

    public function render()
    {
        return view('components.notification-item');
    }
}
