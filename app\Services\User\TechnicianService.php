<?php

namespace App\Services\User;

use App\DTO\Technician\StoreTechnicianData;
use App\Models\Technician;
use App\Repositories\TechnicianRepository;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class TechnicianService
{
    public function __construct(
        private readonly TechnicianRepository $technicianRepository,
        private readonly UserRepository $userRepository
    ) {
        //
    }

    /**
     * Create a new technician.
     */
    public function create(StoreTechnicianData $dto): Technician
    {
        $user = auth('user')->user();

        if ($this->userRepository->hasPendingRequest($user)) {
            throw new BadRequestHttpException(__('You already have a pending upgrade request.'));
        }

        if ($user->technician()->exists()) {
            throw new BadRequestHttpException(__('You already have an approved technician upgrade request.'));
        }

        return DB::transaction(function () use ($user, $dto) {
            if (! empty($dto->userData())) {
                $this->userRepository->updateData($user, $dto->userData());
            }

            return $this->technicianRepository->create($dto->technicianData());
        });
    }
}
