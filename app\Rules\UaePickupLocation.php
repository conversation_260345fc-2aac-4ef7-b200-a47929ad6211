<?php

namespace App\Rules;

use App\Models\Address;
use App\Models\City;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class UaePickupLocation implements ValidationRule
{
    public function __construct(
        protected ?int $pickupAddressId = null,
        protected ?array $pickupLocation = null
    ) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Get location details from either address or coordinates
        $locationDetails = $this->getLocationDetails();

        if ($locationDetails === null) {
            $fail(__('Unable to determine pickup location details.'));

            return;
        }

        // Check if the location is in the UAE
        if (! $this->isUaeLocation($locationDetails)) {
            $fail(__('Pickup location must be within the UAE.'));
        }
    }

    protected function getLocationDetails(): ?array
    {
        // If pickup address ID is provided
        if ($this->pickupAddressId !== null && $this->pickupAddressId !== 0) {
            $address = Address::with(['area.city.country', 'phoneCountry'])->find($this->pickupAddressId);

            if (! $address) {
                return null;
            }

            return [
                'country_code' => $address->area?->city?->country?->abbv ?? $address->country_code ?? null,
                'country_id' => $address->area?->city?->country?->id ?? null,
            ];
        }

        // If pickup location coordinates are provided
        if ($this->pickupLocation && isset($this->pickupLocation['lat'], $this->pickupLocation['lng'])) {
            $pointWKT = "POINT({$this->pickupLocation['lng']} {$this->pickupLocation['lat']})";

            $city = City::with('country')
                ->where('status', 'active')
                ->whereRaw('ST_Contains(area, ST_GeomFromText(?))', [$pointWKT])
                ->first();

            if (! $city) {
                return null;
            }

            return [
                'country_code' => $city->country->abbv ?? null,
                'country_id' => $city->country->id ?? null,
            ];
        }

        return null;
    }

    protected function isUaeLocation(array $locationDetails): bool
    {
        return isset($locationDetails['country_code']) && $locationDetails['country_code'] === 'AE';
    }
}
