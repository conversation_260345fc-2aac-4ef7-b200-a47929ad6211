<?php

namespace App\Http\Controllers\Admin\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ForgotPasswordRequest;
use App\Http\Requests\Admin\ResetPasswordRequest;
use App\Repositories\AdminRepository;
use App\Traits\HasOtp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class PasswordResetController extends Controller
{
    use HasOtp;

    const OTP_RESEND_BLOCK_HOURS = 60 * 60 * 12;

    public function __construct(private readonly AdminRepository $adminRepository) {}

    public function forgotPassowrd()
    {
        return view('auth.forgot-password');
    }

    public function sendResetCode(ForgotPasswordRequest $request)
    {
        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername($request->username);
        $adminByPhone = $this->adminRepository->getByPhone($request->username);
        $admin = $adminByEmailOrUsername ?? $adminByPhone;
        $channel = $adminByEmailOrUsername ? 'email' : 'phone';

        if (!$admin) {
            throw ValidationException::withMessages([
                'username' => __('user not found.'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("forgot-password-attempts-admin:$admin->id", 3)) {

            $seconds = RateLimiter::availableIn("forgot-password-attempts-admin:$admin->id");
            $remainingTime = formatSecondsToHoursTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('limit reached: retry after :time hours', ['time' => $remainingTime]),
            ]);
        }

        // send code if hasn't reached limit
        if (!RateLimiter::tooManyAttempts("forgot-password-attempts-admin:$admin->id", 3)) {
            $code = $this->generateUniqueOtpCode();

            $admin->resetCodes()->create([
                'code' => $code,
                'expires_at' => now()->addHours(3)
            ]);

            if ($adminByEmailOrUsername) {
                $this->dispatchOtp('email', $admin->email, $code);
            }

            if ($adminByPhone) {
                $this->dispatchOtp('phone', $admin->phone, $code);
            }
        }

        RateLimiter::hit("forgot-password-attempts-admin:$admin->id", self::OTP_RESEND_BLOCK_HOURS);

        $channel = $adminByEmailOrUsername ? 'email' : 'phone';

        session()->flash('username', $request->username);
        session()->flash('channel', $channel);

        return to_route('admin.confirm-reset-code');
    }

    public function confirmResetCodeView()
    {
        if (!session()->has('username')) {
            return to_route('admin.forgot-password');
        }

        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername(session('username'));
        $adminByPhone = $this->adminRepository->getByPhone(session('username'));
        $admin = $adminByEmailOrUsername ?? $adminByPhone;

        $can_resend = !RateLimiter::tooManyAttempts("forgot-password-attempts-admin:$admin->id", 3);

        $lastOtp = $admin->resetCodes()->latest()->first();

        $available_in = null;

        if ($lastOtp) {
            $secondsDiff = abs(now()->diffInSeconds($lastOtp->created_at));

            // If between 1 and 60 seconds, set remaining wait time
            if ($secondsDiff >= 0 && $secondsDiff <= 60) {
                $available_in = (int) (60 - $secondsDiff);
            }
        }

        return view('auth.confirm-otp', compact('can_resend', 'available_in'));
    }

    public function confirmResetCode(Request $request)
    {
        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername($request->username);
        $adminByPhone = $this->adminRepository->getByPhone($request->username);
        $admin = $adminByEmailOrUsername ?? $adminByPhone;

        if (!$admin) {
            throw ValidationException::withMessages([
                'username' => __('user not found.'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("confirm-reset-attempts-admin:$admin->id", 3)) {
            $seconds = RateLimiter::availableIn("confirm-reset-attempts-admin:$admin->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' =>  __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("confirm-reset-attempts-admin:$admin->id", 120);

        $otp = $admin->resetCodes()->where('code', $request->otp)->latest()->first();

        if (!$otp) {
            throw ValidationException::withMessages([
                'otp' => __('invalid otp'),
            ]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages([
                'otp' => __('expired otp'),
            ]);
        }

        session()->flash('username', $request->username);
        session()->flash('otp', $request->otp);

        return success(true);
    }

    public function resetPasswordView()
    {
        if (! session()->has('username') || ! session()->has('otp')) {
            return redirect()->route('admin.forgot-password');
        }

        return view('auth.reset-password');
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername($request->username);
        $adminByPhone = $this->adminRepository->getByPhone($request->username);
        $admin = $adminByEmailOrUsername ?? $adminByPhone;

        if (!$admin) {
            throw ValidationException::withMessages([
                'username' => __('user not found.'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("reset-password-attempts-admin:$admin->id", 3)) {
            $seconds = RateLimiter::availableIn("reset-password-attempts-admin:$admin->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' =>  __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("reset-password-attempts-admin:$admin->id", 120);

        $otp = $admin->resetCodes()->where('code', $request->otp)->latest()->first();

        if (!$otp) {
            throw ValidationException::withMessages([
                'otp' => __('invalid otp'),
            ]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages([
                'otp' => __('expired otp'),
            ]);
        }

        DB::transaction(function () use ($admin, $request) {
            $admin->update(['password' => bcrypt($request->password)]);
            $admin->resetCodes()->delete();
        });

        return to_route('admin.login');
    }
}
