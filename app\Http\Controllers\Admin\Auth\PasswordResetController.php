<?php

namespace App\Http\Controllers\Admin\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ForgotPasswordRequest;
use App\Http\Requests\Admin\ResetPasswordRequest;
use App\Repositories\AdminRepository;
use App\Traits\HasOtp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class PasswordResetController extends Controller
{
    use HasOtp;

    /** Attempt Limits */
    private const SEND_RESET_CODE_ATTEMPTS_LIMIT = 3;

    private const CONFIRM_RESET_CODE_ATTEMPTS_LIMIT = 3;

    private const RESET_PASSWORD_ATTEMPTS_LIMIT = 3;

    /** Lockout Durations */
    private const ATTEMPT_LOCK_SECONDS = 120; // 2 minutes

    private const RESET_CODE_RESEND_BLOCK_SECONDS = 60 * 60 * 12; // 12 hours

    private const RESET_CODE_RESEND_WAIT_SECONDS = 60; // 1 minute cooldown for resend

    /** OTP Expiry */
    private const RESET_CODE_EXPIRY_HOURS = 6;

    public function __construct(private readonly AdminRepository $adminRepository) {}

    /** Forgot Password View */
    public function forgotPassword()
    {
        return view('auth.forgot-password');
    }

    /** Send Reset Code */
    public function sendResetCode(ForgotPasswordRequest $request)
    {
        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername($request->username);
        $adminByPhone = $this->adminRepository->getByPhone($request->username);
        $admin = $adminByEmailOrUsername ?? $adminByPhone;
        $channel = $adminByEmailOrUsername ? 'email' : 'phone';

        if (! $admin) {
            throw ValidationException::withMessages(['username' => __('user not found.')]);
        }

        if (
            ! RateLimiter::tooManyAttempts("send-reset-code-admin:$admin->id", self::SEND_RESET_CODE_ATTEMPTS_LIMIT)
            && ! RateLimiter::tooManyAttempts("reset-code-resend-wait-admin:$admin->id", 1)
        ) {
            $code = $this->generateUniqueOtpCode();

            $admin->resetCodes()->create([
                'code' => $code,
                'expires_at' => now()->addHours(self::RESET_CODE_EXPIRY_HOURS),
            ]);

            if ($adminByEmailOrUsername) {
                $this->dispatchOtp('email', $admin->email, $code);
            }

            if ($adminByPhone) {
                $this->dispatchOtp('phone', $admin->phone, $code);
            }

            // Cooldown & attempt tracking
            RateLimiter::hit("reset-code-resend-wait-admin:$admin->id", self::RESET_CODE_RESEND_WAIT_SECONDS);
            RateLimiter::hit("send-reset-code-admin:$admin->id", self::RESET_CODE_RESEND_BLOCK_SECONDS);
        }

        session()->flash('username', $request->username);
        session()->flash('channel', $channel);

        return to_route('admin.confirm-reset-code');
    }

    /** Confirm Reset Code View */
    public function confirmResetCodeView()
    {
        if (! session()->has('username')) {
            return to_route('admin.forgot-password');
        }

        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername(session('username'));
        $adminByPhone = $this->adminRepository->getByPhone(session('username'));
        $admin = $adminByEmailOrUsername ?? $adminByPhone;

        $can_resend = ! RateLimiter::tooManyAttempts("send-reset-code-admin:$admin->id", self::SEND_RESET_CODE_ATTEMPTS_LIMIT);
        $wait_seconds = RateLimiter::availableIn("reset-code-resend-wait-admin:$admin->id");

        return view('auth.confirm-otp', compact('can_resend', 'wait_seconds'));
    }

    /** Confirm Reset Code */
    public function confirmResetCode(Request $request)
    {
        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername($request->username);
        $adminByPhone = $this->adminRepository->getByPhone($request->username);
        $admin = $adminByEmailOrUsername ?? $adminByPhone;

        if (! $admin) {
            throw ValidationException::withMessages(['username' => __('user not found.')]);
        }

        if (RateLimiter::tooManyAttempts("confirm-reset-attempts-admin:$admin->id", self::CONFIRM_RESET_CODE_ATTEMPTS_LIMIT)) {
            $seconds = RateLimiter::availableIn("confirm-reset-attempts-admin:$admin->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'otp' => __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("confirm-reset-attempts-admin:$admin->id", self::ATTEMPT_LOCK_SECONDS);

        $otp = $admin->resetCodes()->where('code', $request->otp)->latest()->first();

        if (! $otp) {
            throw ValidationException::withMessages(['otp' => __('invalid otp')]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages(['otp' => __('expired otp')]);
        }

        session()->flash('username', $request->username);
        session()->flash('otp', $request->otp);

        return success(true);
    }

    /** Reset Password View */
    public function resetPasswordView()
    {
        if (! session()->has('username') || ! session()->has('otp')) {
            return redirect()->route('admin.forgot-password');
        }

        return view('auth.reset-password');
    }

    /** Reset Password */
    public function resetPassword(ResetPasswordRequest $request)
    {
        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername($request->username);
        $adminByPhone = $this->adminRepository->getByPhone($request->username);
        $admin = $adminByEmailOrUsername ?? $adminByPhone;

        if (! $admin) {
            throw ValidationException::withMessages(['username' => __('user not found.')]);
        }

        if (RateLimiter::tooManyAttempts("reset-password-attempts-admin:$admin->id", self::RESET_PASSWORD_ATTEMPTS_LIMIT)) {
            $seconds = RateLimiter::availableIn("reset-password-attempts-admin:$admin->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("reset-password-attempts-admin:$admin->id", self::ATTEMPT_LOCK_SECONDS);

        $otp = $admin->resetCodes()->where('code', $request->otp)->latest()->first();

        if (! $otp) {
            throw ValidationException::withMessages(['otp' => __('invalid otp')]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages(['otp' => __('expired otp')]);
        }

        DB::transaction(function () use ($admin, $request) {
            $admin->update(['password' => bcrypt($request->password)]);
            $admin->resetCodes()->delete();
        });

        return to_route('admin.login');
    }

    /** Resend Reset Code */
    public function resendResetCode(Request $request)
    {
        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername($request->username);
        $adminByPhone = $this->adminRepository->getByPhone($request->username);
        $admin = $adminByEmailOrUsername ?? $adminByPhone;

        if (! $admin) {
            throw ValidationException::withMessages(['username' => __('user not found.')]);
        }

        // Check resend cooldown
        if (RateLimiter::tooManyAttempts("reset-code-resend-wait-admin:$admin->id", 1)) {
            $wait_seconds = RateLimiter::availableIn("reset-code-resend-wait-admin:$admin->id");
            $remainingTime = formatSecondsToMinutesTime($wait_seconds);

            throw ValidationException::withMessages([
                'username' => __('please wait :time minutes before requesting a new reset code', ['time' => $remainingTime]),
            ]);
        }

        // Check global resend limit
        if (RateLimiter::tooManyAttempts("send-reset-code-admin:$admin->id", self::SEND_RESET_CODE_ATTEMPTS_LIMIT)) {
            $seconds = RateLimiter::availableIn("send-reset-code-admin:$admin->id");
            $remainingTime = formatSecondsToHoursTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('limit reached: retry after :time hours', ['time' => $remainingTime]),
            ]);
        }

        // Send new reset code
        $code = $this->generateUniqueOtpCode();

        $admin->resetCodes()->create([
            'code' => $code,
            'expires_at' => now()->addHours(self::RESET_CODE_EXPIRY_HOURS),
        ]);

        if ($adminByEmailOrUsername) {
            $this->dispatchOtp('email', $admin->email, $code);
        }

        if ($adminByPhone) {
            $this->dispatchOtp('phone', $admin->phone, $code);
        }

        RateLimiter::hit("reset-code-resend-wait-admin:$admin->id", self::RESET_CODE_RESEND_WAIT_SECONDS);
        RateLimiter::hit("send-reset-code-admin:$admin->id", self::RESET_CODE_RESEND_BLOCK_SECONDS);

        return success([
            'can_resend' => ! RateLimiter::tooManyAttempts("send-reset-code-admin:$admin->id", self::SEND_RESET_CODE_ATTEMPTS_LIMIT),
        ]);
    }
}
