<?php

namespace App\Services\User;

use App\DTO\User\OtpLoginData;
use App\DTO\User\OtpVerificationData;
use App\Http\Resources\User\AuthResource;
use App\Repositories\UserRepository;
use App\Traits\HasOtp;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OtpAuthService
{
    use HasOtp {
        verifyOtp as verifyOtpCode;
    }

    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly GuestAuthService $guestAuthService
    ) {
        //
    }

    /**
     * Send OTP for authentication
     */
    public function sendOtp(OtpLoginData $data): string
    {
        $user = $this->userRepository->findByIdentifier(
            $data->identifier,
            $data->country_code
        );

        if (! $user instanceof \App\Models\User) {
            throw new NotFoundHttpException(__('User not found'));
        }

        $type = filter_var($data->identifier, FILTER_VALIDATE_EMAIL) ? 'email' : 'phone';

        return $this->generateAndSendOtp(
            $user,
            $type,
            $data->identifier,
            $data->country_code
        );
    }

    /**
     * Verify OTP and authenticate user
     */
    public function verifyOtp(OtpVerificationData $data): array
    {
        $user = $this->userRepository->findByIdentifier(
            $data->identifier,
            $data->country_code
        );

        if (! $user instanceof \App\Models\User) {
            throw new NotFoundHttpException(__('User not found'));
        }

        $type = filter_var($data->identifier, FILTER_VALIDATE_EMAIL) ? 'email' : 'phone';

        if (! $this->verifyOtpCode($user, $type, $data->identifier, $data->otp, $data->country_code)) {
            throw ValidationException::withMessages([
                'otp' => [__('Invalid or expired OTP')],
            ]);
        }

        // Mark user as verified
        if (! $user->is_verified) {
            $user->update(['is_verified' => true]);
        }

        if ($data->device_id !== null && $data->device_id !== '' && $data->device_id !== '0') {
            $user->updateDeviceToken([
                'device_id' => $data->device_id,
                'firebase_token' => $data->firebase_token,
            ]);
        }

        if ($guestToken = request()->header('X-GUEST-TOKEN')) {
            $this->guestAuthService->mergeGuestIntoUser($guestToken, $user);
        }

        return [
            'user' => new AuthResource($user),
            'token' => $user->createToken('otp-token')->plainTextToken,
        ];
    }

    public function canSendOtp(array $data): bool
    {
        $user = $this->userRepository->findByIdentifier(
            $data['identifier'],
            $data['country_code']
        );

        if (! $user instanceof \App\Models\User) {
            throw new NotFoundHttpException(__('User not found'));
        }

        $type = filter_var($data['identifier'], FILTER_VALIDATE_EMAIL) ? 'email' : 'phone';

        return $this->hasOtpTriesLeft($type, $data['identifier'], $data['country_code']);
    }
}
