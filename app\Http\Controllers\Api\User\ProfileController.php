<?php

namespace App\Http\Controllers\Api\User;

use App\DTO\User\UpdateProfileData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\UpdateProfileRequest;
use App\Http\Resources\User\UserResource;
use App\Services\User\ProfileSerivce;

class ProfileController extends Controller
{
    public function __construct(private readonly ProfileSerivce $profileService)
    {
        //
    }

    public function index()
    {
        return success(new UserResource(auth()->user()));
    }

    public function update(UpdateProfileRequest $request)
    {
        $dto = UpdateProfileData::from($request->validated());
        $user = $this->profileService->update($dto);

        return success(new UserResource($user));
    }

    public function toggleBiometric()
    {
        $status = $this->profileService->toggleBiometric();

        return success(['biometric_enabled' => $status]);
    }

    public function toggleNotifications()
    {
        $status = $this->profileService->toggleNotifications();

        return success(['notification_enabled' => $status]);
    }

    public function deleteAccount()
    {
        $this->profileService->deleteAccount();

        return success(__('Your account has been deleted successfully.'));
    }
}
