<?php

namespace App\Services\Admin;

use App\Mail\AccountCredentialsMail;
use App\Models\Supplier;
use App\Models\Technician;
use App\Models\User;
use App\Repositories\SupplierRepository;
use App\Repositories\TechnicianRepository;
use App\Repositories\UserRepository;
use App\Services\MediaUploaderService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use MatanYadaev\EloquentSpatial\Objects\Point;

class UserService
{
    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly SupplierRepository $supplierRepository,
        private readonly TechnicianRepository $technicianRepository,
        private readonly MediaUploaderService $mediaService
    ) {}

    public function create(array $data): void
    {
        $data['country_code'] = '966';
        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['location_address'] = $data['location']['address'] ?? null;
        $data['location'] = isset($data['location']['lat']) ? new Point($data['location']['lat'], $data['location']['lng']) : null;

        $roles = collect($data['roles'] ?? []);

        if ($roles->count() == 0) {
            $data = Arr::except($data, ['location', 'location_address', 'bank_name', 'bank_account_number', 'iban']);
        }

        DB::transaction(function () use ($data, $roles): void {

            $user = $this->userRepository->create($data);

            if (isset($data['image'])) {
                $this->mediaService->upload($user, $data['image']);
            }

            if ($roles->contains('supplier')) {
                $data['supplier']['approval_status'] = 'approved';
                $supplier = $user->supplier()->create($data['supplier']);
                $supplier->categories()->attach($data['supplier']['categories']);

                if (isset($data['supplier']['company_image'])) {
                    $this->mediaService->upload($supplier, $data['supplier']['company_image']);
                }
            }

            if ($roles->contains('technician')) {
                $data['technician']['approval_status'] = 'approved';
                $technician = $user->technician()->create($data['technician']);
                $technician->specializations()->attach($data['technician']['specializations']);
            }

            if ($roles->contains('rental_outlet')) {
                $user->rentalOutlet()->create(['approval_status' => 'approved']);
            }

            $shouldHavePassword = $roles->contains('supplier') || $roles->contains('rental_outlet');

            if ($shouldHavePassword) {
                $password = Str::password(12);

                $this->userRepository->update($user, ['password' => bcrypt($password)]);

                Mail::to($user->email)->send(new AccountCredentialsMail($user, $password));
            }
        });
    }

    public function update(User $user, array $data): void
    {
        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['location_address'] = $data['location']['address'] ?? null;
        $data['location'] = isset($data['location']['lat']) ? new Point($data['location']['lat'], $data['location']['lng']) : null;

        $roles = collect($data['roles'] ?? []);

        if ($roles->count() == 0) {
            // remove shared data between roles
            $data_to_be_null = ['location', 'location_address', 'bank_name', 'bank_account_number', 'iban'];
            foreach ($data_to_be_null as $key) {
                $data[$key] = null;
            }

            $data['password'] = null;
        }

        DB::transaction(function () use ($user, $data, $roles): void {

            $this->userRepository->update($user, $data);

            if (isset($data['image'])) {
                $user->clearMediaCollection();
                $this->mediaService->upload($user, $data['image']);
            }

            // delete roles that are not selected
            if ($user->technician && ! $roles->contains('technician')) {
                $this->deleteTechnician($user->technician);
            }

            if ($user->supplier && ! $roles->contains('supplier')) {
                $this->deleteSupplier($user->supplier);
            }

            if ($user->rentalOutlet && ! $roles->contains('rental_outlet')) {
                $user->rentalOutlet()->delete();
            }

            // create password for user if hew has new role that requires password
            $shouldHavePassword =
                (! $user->roles->contains('supplier') && ! $user->roles->contains('rental_outlet'))
                && ($roles->contains('supplier') || $roles->contains('rental_outlet'));

            // update or create roles
            if ($roles->contains('supplier')) {
                $supplier = $user->supplier()->updateOrCreate(
                    ['approval_status' => 'approved'],
                    $data['supplier']
                );

                $supplier->categories()->sync($data['supplier']['categories']);

                if (isset($data['supplier']['company_image'])) {
                    $supplier->clearMediaCollection();
                    $this->mediaService->upload($supplier, $data['supplier']['company_image']);
                }
            }

            if ($roles->contains('technician')) {
                $technician = $user->technician()->updateOrCreate(
                    ['approval_status' => 'approved'],
                    $data['technician']
                );

                $technician->specializations()->sync($data['technician']['specializations']);
            }

            if ($roles->contains('rental_outlet')) {
                $user->rentalOutlet()->updateOrCreate(['approval_status' => 'approved']);
            }

            if ($shouldHavePassword) {
                $password = Str::password(12);

                $this->userRepository->update($user, ['password' => bcrypt($password)]);

                Mail::to($user->email)->send(new AccountCredentialsMail($user, $password));
            }
        });
    }

    public function delete(User $user): void
    {
        DB::transaction(function () use ($user): void {
            $user->delete();
            $this->userRepository->invalidateUniqueData($user);

            if ($user->supplier) {
                $this->deleteSupplier($user->supplier);
            }

            if ($user->technician) {
                $this->deleteTechnician($user->technician);
            }

            if ($user->rentalOutlet) {
                $user->rentalOutlet()->delete();
            }
        });
    }

    public function deleteSupplier(Supplier $supplier): void
    {
        $supplier->delete();
        $this->supplierRepository->invalidateUniqueData($supplier);
    }

    public function deleteTechnician(Technician $technician): void
    {
        $technician->delete();
        $this->technicianRepository->invalidateUniqueData($technician);
    }

    public function toggleStatus(User $user)
    {
        $user->is_active = ! $user->is_active;
        $user->save();
    }
}
