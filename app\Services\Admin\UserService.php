<?php

namespace App\Services\Admin;

use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class UserService
{
    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly MediaUploaderService $mediaService
    ) {}

    public function create(): void
    {
        $data = request()->all();

        $data['phone'] = normalizePhoneNumber($data['phone']);

        DB::transaction(function () use ($data): void {
            $user = $this->userRepository->create($data);

            if (request('image')) {
                $this->mediaService->upload($user, request('image'), 'users');
            }
        });
    }

    public function update(User $user): void
    {
        $data = request()->all();

        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['status'] = request('status') ? 'active' : 'inactive';
        $data['password'] = request('password') ? bcrypt(request('password')) : $user->password;

        DB::transaction(function () use ($user, $data): void {
            $this->userRepository->update($user, $data);

            if (request('image')) {
                $this->mediaService->delete($user->image);
                $this->mediaService->upload($user, request('image'), 'users');
            }

            if ($data['status'] == 'inactive') {
                $user->tokens()->delete();
            }
        });
    }

    public function delete(User $user): void
    {
        $hasActiveOrder = $this->userRepository->hasActiveOrder($user);

        if ($hasActiveOrder) {
            throw new BadRequestHttpException(__('user has an active order'));
        }

        DB::transaction(function () use ($user): void {
            $user->delete();
            $this->userRepository->invalidateUniqueData($user);
        });
    }
}
