<x-layout :title="__('Users')">
    <x-session-message />
    <div class="card">
        <div class="card-heading">
            <p>{{ __('Users') }}</p>
            <div>
                @can('create user')
                <a class="btn btn-sm btn-outline-primary" href="{{ route('admin.users.create') }}">{{ __('Create') }}</a>
                @endcan
            </div>
        </div>
        <div class="row" id="filters" data-datatable-id="users-table">
            <!-- Search -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Search') }}</b></label>
                <input type="text" name="search_param" class="form-control" onchange="dt_filter()">
            </div>

            <!-- Status -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Status') }}</b></label>
                <select name="status" class="select2 form-select form-select-lg" data-placeholder="{{ __('Choose') }}..." data-allow-clear="true" onchange="dt_filter()">
                    <option></option>
                    <option value="1">{{ __('Active') }}</option>
                    <option value="0">{{ __('Inactive') }}</option>
                </select>
            </div>

            <!-- Type -->
            <div class="mb-3 col-sm-6 col-lg-2">
                <label class="form-label"><b>{{ __('Type') }}</b></label>
                <select name="type" class="select2 form-select form-select-lg" data-allow-clear="true" onchange="dt_filter()">
                    <option value="all">{{ __('All') }}</option>
                    <option value="client">{{ __('client') }}</option>
                    <option value="supplier">{{ __('supplier') }}</option>
                    <option value="technician">{{ __('technician') }}</option>
                    <option value="rentalOutlet">{{ __('rental operator') }}</option>
                </select>
            </div>
        </div>
        <div class="card-body">
            <div class="card-datatable table-responsive pt-0">
                {{ $dataTable->table(['class' => 'datatables-basic table table-striped']) }}
            </div>
            <x-delete-modal />
        </div>
    </div>

    @push('css')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css') }}" />
    <style>
        /* ======= Container / Card ======= */
.card, .dt-row {
    background-color: #fff;
    border-radius: 16px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05);
    padding: 1rem;
}

/* ======= DataTable General ======= */
table.dataTable {
    width: 100% !important;
    border-collapse: separate !important;
    border-spacing: 0 8px; /* space between rows */
}

table.dataTable thead th {
    background-color: #2C3E50 !important;
    color: #fff !important;
    font-weight: 600;
    border: none !important;
    text-align: center !important;
    vertical-align: middle;
    padding: 0.75rem;
}

table.dataTable tbody tr {
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.04);
    transition: all 0.2s ease;
}

table.dataTable tbody tr:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
}

table.dataTable tbody td {
    border: none !important;
    text-align: center !important;
    vertical-align: middle;
    padding: 0.9rem 0.5rem;
}

/* ======= Badges (Status) ======= */
.badge.bg-success {
    background-color: #E8FFF1 !important;
    color: #00B06A !important;
    font-weight: 600;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 0.85rem;
}

.badge.bg-danger {
    background-color: #FFEAEA !important;
    color: #E74C3C !important;
    font-weight: 600;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 0.85rem;
}

/* ======= Actions column ======= */
.actions {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.75rem;
}

.actions a, .actions button {
    background: none;
    border: none;
    color: #6c757d;
    font-size: 1.1rem;
    transition: color 0.2s;
}

.actions a:hover, .actions button:hover {
    color: #007bff;
}

.actions i {
    cursor: pointer;
}

/* ======= Pagination ======= */
.dataTables_wrapper .dataTables_paginate {
    margin-top: 1.2rem;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border: none !important;
    background: transparent !important;
    color: #6c757d !important;
    padding: 6px 10px;
    margin: 0 3px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current {
    background-color: #007bff !important;
    color: #fff !important;
    font-weight: 600;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background-color: #007bff !important;
    color: #fff !important;
}

/* ======= Search Box and Filters ======= */
.dataTables_filter input {
    border-radius: 10px;
    border: 1px solid #ddd;
    padding: 6px 10px;
}

.dataTables_filter input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.15rem rgba(0,123,255,0.15);
    outline: none;
}

/* ======= Table Header Sorting Indicators ======= */
table.dataTable thead th.sorting:after,
table.dataTable thead th.sorting_asc:after,
table.dataTable thead th.sorting_desc:after {
    opacity: 0.4;
    margin-right: 5px;
    color: #aaa;
}

    </style>
    @endpush

    @push('js')
    <script src="{{ asset('assets/vendor/libs/datatables-bs5/datatables-bootstrap5.js') }}"></script>
    {{ $dataTable->scripts(attributes: ['type' => 'module']) }}
    @endpush
</x-layout>