<?php

namespace App\Http\Requests\Api\User\Auth;

use App\Rules\ValidPhone;
use Illuminate\Foundation\Http\FormRequest;

class SendOtpRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return $this->isEmail()
            ? $this->emailRules()
            : $this->phoneRules();
    }

    protected function emailRules(): array
    {
        return [
            'identifier' => ['required', 'string', 'email'],
        ];
    }

    protected function phoneRules(): array
    {
        return [
            'identifier' => ['required', 'string', new ValidPhone($this->input('country_code'))],
            'country_code' => ['required', 'regex:/^\d{1,4}$/'],
        ];
    }

    private function isEmail(): bool
    {
        return filter_var($this->input('identifier'), FILTER_VALIDATE_EMAIL) !== false;
    }
}
