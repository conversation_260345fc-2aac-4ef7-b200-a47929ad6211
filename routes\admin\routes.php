<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\HomeController;
use App\Http\Controllers\Admin\ProfileController;
use App\Http\Controllers\Admin\UserController;
use Illuminate\Support\Facades\Route;

include base_path('routes/admin/auth.php');

Route::group(
    ['middleware' => ['auth:admin', 'checkStatus']],
    function () {
        Route::get('/', [HomeController::class, 'index'])->name('index');
        Route::resource('admins', AdminController::class);
        Route::resource('users', UserController::class);
        Route::post('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

        Route::get('profile', [ProfileController::class, 'index'])->name('profile');
        Route::post('profile', [ProfileController::class, 'update']);
    }
);
