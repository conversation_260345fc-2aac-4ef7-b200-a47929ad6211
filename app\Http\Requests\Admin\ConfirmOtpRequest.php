<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;

class ConfirmOtpRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'username' => ['required', 'string'],
            'password' => ['required', 'string'],
            'otp' => ['required', 'string'],
        ];
    }

    protected function prepareForValidation(): void
    {
        // Combine otp array into single string if present
        if ($this->has('otp') && is_array($this->otp)) {
            $this->merge([
                'otp' => implode('', $this->otp),
            ]);
        }
    }
}
