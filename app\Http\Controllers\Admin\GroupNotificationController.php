<?php

namespace App\Http\Controllers\Admin;

use App\DataTables\Admin\GroupNotificationDataTable;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GroupNotification\StoreRequest;
use App\Http\Requests\Admin\GroupNotification\UpdateRequest;
use App\Models\GroupNotification;
use App\Repositories\GroupNotificationRepository;
use App\Services\Admin\GroupNotificationService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class GroupNotificationController extends Controller
{
    public function __construct(
        private readonly GroupNotificationService $service,
        private readonly GroupNotificationRepository $repo
    ) {}

    public function index(GroupNotificationDataTable $dataTable)
    {
        return $dataTable->render('pages.admin.notifications.index');
    }

    public function create(): View
    {
        return view('pages.admin.notifications.create');
    }

    public function store(StoreRequest $request): RedirectResponse
    {
        $this->service->store($request->validated());

        return redirect()->route('admin.notifications.index')
            ->with('success', 'Notification created and sent successfully.');
    }

    public function show(GroupNotification $notification): View
    {
        $notification->load('receivers.receiver');

        return view('pages.admin.notifications.show', compact('notification'));
    }

    public function edit(GroupNotification $notification): View
    {
        $notification->load('receivers.receiver');

        return view('pages.admin.notifications.edit', compact('notification'));
    }

    public function update(UpdateRequest $request, GroupNotification $notification): RedirectResponse
    {
        $this->service->update($notification, $request->validated());

        return redirect()->route('admin.notifications.index')
            ->with('success', 'Notification updated successfully.');
    }

    public function clone(GroupNotification $notification): View
    {
        $notification->load('receivers.receiver');

        return view('pages.admin.notifications.clone', compact('notification'));
    }

    public function getReceivers(Request $request): array
    {
        return $this->repo->getReceivers($request->input('type'))
            ->map(fn ($model) => ['id' => $model->id, 'name' => $model->name])
            ->toArray();
    }

    public function destroy(GroupNotification $notification): RedirectResponse
    {
        return $this->service->delete($notification);
    }
}
