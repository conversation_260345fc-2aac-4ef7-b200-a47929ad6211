<?php

namespace App\Http\Controllers\Provider\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ForgotPasswordRequest;
use App\Http\Requests\Admin\ResetPasswordRequest;
use App\Repositories\UserRepository;
use App\Traits\HasOtp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class PasswordResetController extends Controller
{
    use HasOtp;

    /** Attempt Limits */
    private const SEND_RESET_CODE_ATTEMPTS_LIMIT = 3;

    private const CONFIRM_RESET_CODE_ATTEMPTS_LIMIT = 3;

    private const RESET_PASSWORD_ATTEMPTS_LIMIT = 3;

    /** Lockout Durations */
    private const ATTEMPT_LOCK_SECONDS = 120; // 2 minutes

    private const RESET_CODE_RESEND_BLOCK_SECONDS = 60 * 60 * 12; // 12 hours

    private const RESET_CODE_RESEND_WAIT_SECONDS = 60; // 1 minute cooldown for resend

    /** OTP Expiry */
    private const RESET_CODE_EXPIRY_HOURS = 6;

    public function __construct(private readonly UserRepository $userRepository) {}

    /** Forgot Password View */
    public function forgotPassword()
    {
        return view('auth.forgot-password');
    }

    /** Send Reset Code */
    public function sendResetCode(ForgotPasswordRequest $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;
        $channel = $userByEmailOrUsername ? 'email' : 'phone';

        if (! $user) {
            throw ValidationException::withMessages(['username' => __('user not found.')]);
        }

        if (
            ! RateLimiter::tooManyAttempts("send-reset-code-provider:$user->id", self::SEND_RESET_CODE_ATTEMPTS_LIMIT)
            && ! RateLimiter::tooManyAttempts("reset-code-resend-wait-provider:$user->id", 1)
        ) {
            $code = $this->generateUniqueOtpCode();

            $user->resetCodes()->create([
                'code' => $code,
                'expires_at' => now()->addHours(self::RESET_CODE_EXPIRY_HOURS),
            ]);

            if ($userByEmailOrUsername) {
                $this->dispatchOtp('email', $user->email, $code);
            }

            if ($userByPhone) {
                $this->dispatchOtp('phone', $user->phone, $code);
            }

            // Cooldown & attempt tracking
            RateLimiter::hit("reset-code-resend-wait-provider:$user->id", self::RESET_CODE_RESEND_WAIT_SECONDS);
            RateLimiter::hit("send-reset-code-provider:$user->id", self::RESET_CODE_RESEND_BLOCK_SECONDS);
        }

        session()->flash('username', $request->username);
        session()->flash('channel', $channel);

        return to_route('provider.confirm-reset-code');
    }

    /** Confirm Reset Code View */
    public function confirmResetCodeView()
    {
        if (! session()->has('username')) {
            return to_route('provider.forgot-password');
        }

        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername(session('username'));
        $userByPhone = $this->userRepository->getByPhone(session('username'));
        $user = $userByEmailOrUsername ?? $userByPhone;

        $can_resend = ! RateLimiter::tooManyAttempts("send-reset-code-provider:$user->id", self::SEND_RESET_CODE_ATTEMPTS_LIMIT);
        $wait_seconds = RateLimiter::availableIn("reset-code-resend-wait-provider:$user->id");

        return view('auth.confirm-otp', compact('can_resend', 'wait_seconds'));
    }

    /** Confirm Reset Code */
    public function confirmResetCode(Request $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;

        if (! $user) {
            throw ValidationException::withMessages(['username' => __('user not found.')]);
        }

        if (RateLimiter::tooManyAttempts("confirm-reset-attempts-provider:$user->id", self::CONFIRM_RESET_CODE_ATTEMPTS_LIMIT)) {
            $seconds = RateLimiter::availableIn("confirm-reset-attempts-provider:$user->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'otp' => __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("confirm-reset-attempts-provider:$user->id", self::ATTEMPT_LOCK_SECONDS);

        $otp = $user->resetCodes()->where('code', $request->otp)->latest()->first();

        if (! $otp) {
            throw ValidationException::withMessages(['otp' => __('invalid otp')]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages(['otp' => __('expired otp')]);
        }

        session()->flash('username', $request->username);
        session()->flash('otp', $request->otp);

        return success(true);
    }

    /** Reset Password View */
    public function resetPasswordView()
    {
        if (! session()->has('username') || ! session()->has('otp')) {
            return redirect()->route('provider.forgot-password');
        }

        return view('auth.reset-password');
    }

    /** Reset Password */
    public function resetPassword(ResetPasswordRequest $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;

        if (! $user) {
            throw ValidationException::withMessages(['username' => __('user not found.')]);
        }

        if (RateLimiter::tooManyAttempts("reset-password-attempts-provider:$user->id", self::RESET_PASSWORD_ATTEMPTS_LIMIT)) {
            $seconds = RateLimiter::availableIn("reset-password-attempts-provider:$user->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("reset-password-attempts-provider:$user->id", self::ATTEMPT_LOCK_SECONDS);

        $otp = $user->resetCodes()->where('code', $request->otp)->latest()->first();

        if (! $otp) {
            throw ValidationException::withMessages(['otp' => __('invalid otp')]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages(['otp' => __('expired otp')]);
        }

        DB::transaction(function () use ($user, $request) {
            $user->update(['password' => bcrypt($request->password)]);
            $user->resetCodes()->delete();
        });

        return to_route('provider.login');
    }

    /** Resend Reset Code */
    public function resendResetCode(Request $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;

        if (! $user) {
            throw ValidationException::withMessages(['username' => __('user not found.')]);
        }

        // Check resend cooldown
        if (RateLimiter::tooManyAttempts("reset-code-resend-wait-provider:$user->id", 1)) {
            $wait_seconds = RateLimiter::availableIn("reset-code-resend-wait-provider:$user->id");
            $remainingTime = formatSecondsToMinutesTime($wait_seconds);

            throw ValidationException::withMessages([
                'username' => __('please wait :time minutes before requesting a new reset code', ['time' => $remainingTime]),
            ]);
        }

        // Check global resend limit
        if (RateLimiter::tooManyAttempts("send-reset-code-provider:$user->id", self::SEND_RESET_CODE_ATTEMPTS_LIMIT)) {
            $seconds = RateLimiter::availableIn("send-reset-code-provider:$user->id");
            $remainingTime = formatSecondsToHoursTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('limit reached: retry after :time hours', ['time' => $remainingTime]),
            ]);
        }

        // Send new reset code
        $code = $this->generateUniqueOtpCode();

        $user->resetCodes()->create([
            'code' => $code,
            'expires_at' => now()->addHours(self::RESET_CODE_EXPIRY_HOURS),
        ]);

        if ($userByEmailOrUsername) {
            $this->dispatchOtp('email', $user->email, $code);
        }

        if ($userByPhone) {
            $this->dispatchOtp('phone', $user->phone, $code);
        }

        RateLimiter::hit("reset-code-resend-wait-provider:$user->id", self::RESET_CODE_RESEND_WAIT_SECONDS);
        RateLimiter::hit("send-reset-code-provider:$user->id", self::RESET_CODE_RESEND_BLOCK_SECONDS);

        return success([
            'can_resend' => ! RateLimiter::tooManyAttempts("send-reset-code-provider:$user->id", self::SEND_RESET_CODE_ATTEMPTS_LIMIT),
        ]);
    }
}
