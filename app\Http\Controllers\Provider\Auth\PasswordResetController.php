<?php

namespace App\Http\Controllers\Provider\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ForgotPasswordRequest;
use App\Http\Requests\Admin\ResetPasswordRequest;
use App\Repositories\AdminRepository;
use App\Repositories\UserRepository;
use App\Traits\HasOtp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class PasswordResetController extends Controller
{
    use HasOtp;

    const OTP_RESEND_BLOCK_HOURS = 60 * 60 * 12;

    public function __construct(private readonly UserRepository $userRepository) {}

    public function forgotPassowrd()
    {
        return view('auth.forgot-password');
    }

    public function sendResetCode(ForgotPasswordRequest $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;
        $channel = $userByEmailOrUsername ? 'email' : 'phone';

        if (!$user || (!$user->supplier && !$user->rentalOutlet)) {
            throw ValidationException::withMessages([
                'username' => __('user not found.'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("forgot-password-attempts-provider:$user->id", 3)) {

            $seconds = RateLimiter::availableIn("forgot-password-attempts-provider:$user->id");
            $remainingTime = formatSecondsToHoursTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('limit reached: retry after :time hours', ['time' => $remainingTime]),
            ]);
        }

        // send code if hasn't reached limit
        if (!RateLimiter::tooManyAttempts("forgot-password-attempts-provider:$user->id", 3)) {
            $code = $this->generateUniqueOtpCode();

            $user->resetCodes()->create([
                'code' => $code,
                'expires_at' => now()->addHours(3)
            ]);

            if ($userByEmailOrUsername) {
                $this->dispatchOtp('email', $user->email, $code);
            }

            if ($userByPhone) {
                $this->dispatchOtp('phone', $user->phone, $code);
            }
        }

        RateLimiter::hit("forgot-password-attempts-provider:$user->id", self::OTP_RESEND_BLOCK_HOURS);

        $channel = $userByEmailOrUsername ? 'email' : 'phone';

        session()->flash('username', $request->username);
        session()->flash('channel', $channel);

        return to_route('provider.confirm-reset-code');
    }

    public function confirmResetCodeView()
    {
        if (!session()->has('username')) {
            return to_route('provider.forgot-password');
        }

        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername(session('username'));
        $userByPhone = $this->userRepository->getByPhone(session('username'));
        $user = $userByEmailOrUsername ?? $userByPhone;

        $can_resend = !RateLimiter::tooManyAttempts("forgot-password-attempts-provider:$user->id", 3);

        $lastOtp = $user->resetCodes()->latest()->first();

        $available_in = null;

        if ($lastOtp) {
            $secondsDiff = abs(now()->diffInSeconds($lastOtp->created_at));

            // If between 1 and 60 seconds, set remaining wait time
            if ($secondsDiff >= 0 && $secondsDiff <= 60) {
                $available_in = (int) (60 - $secondsDiff);
            }
        }

        return view('auth.confirm-otp', compact('can_resend', 'available_in'));
    }

    public function confirmResetCode(Request $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;

        if (!$user || (!$user->supplier && !$user->rentalOutlet)) {
            throw ValidationException::withMessages([
                'username' => __('user not found.'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("confirm-reset-attempts-provider:$user->id", 3)) {
            $seconds = RateLimiter::availableIn("confirm-reset-attempts-provider:$user->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' =>  __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("confirm-reset-attempts-provider:$user->id", 120);

        $otp = $user->resetCodes()->where('code', $request->otp)->latest()->first();

        if (!$otp) {
            throw ValidationException::withMessages([
                'otp' => __('invalid otp'),
            ]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages([
                'otp' => __('expired otp'),
            ]);
        }

        session()->flash('username', $request->username);
        session()->flash('otp', $request->otp);

        return success(true);
    }

    public function resetPasswordView()
    {
        if (! session()->has('username') || ! session()->has('otp')) {
            return redirect()->route('provider.forgot-password');
        }

        return view('auth.reset-password');
    }

    public function resetPassword(ResetPasswordRequest $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;

        if (!$user || (!$user->supplier && !$user->rentalOutlet)) {
            throw ValidationException::withMessages([
                'username' => __('auth.failed'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("reset-password-attempts-provider:$user->id", 3)) {
            $seconds = RateLimiter::availableIn("reset-password-attempts-provider:$user->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' =>  __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("reset-password-attempts-provider:$user->id", 120);

        $otp = $user->resetCodes()->where('code', $request->otp)->latest()->first();

        if (!$otp) {
            throw ValidationException::withMessages([
                'otp' => __('invalid otp'),
            ]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages([
                'otp' => __('expired otp'),
            ]);
        }

        DB::transaction(function () use ($user, $request) {
            $user->update(['password' => bcrypt($request->password)]);
            $user->resetCodes()->delete();
        });

        return to_route('provider.login');
    }
}
