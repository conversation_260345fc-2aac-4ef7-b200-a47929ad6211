<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidIban implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_string($value)) {
            $fail(__('validation.custom.iban.not_string'));

            return;
        }

        $iban = strtoupper(str_replace(' ', '', $value));

        if (strlen($iban) !== 24 || ! str_starts_with($iban, 'SA')) {
            $fail(__('validation.custom.iban.length_start'));

            return;
        }

        if (! preg_match('/^SA\d{22}$/', $iban)) {
            $fail(__('validation.custom.iban.format'));

            return;
        }

        $rearranged = substr($iban, 4).substr($iban, 0, 4);

        $numeric = '';
        foreach (str_split($rearranged) as $ch) {
            $numeric .= ctype_alpha($ch) ? (ord($ch) - 55) : $ch;
        }

        $remainder = 0;
        $pos = 0;
        $len = strlen($numeric);

        while ($pos < $len) {
            $chunk = $remainder.substr($numeric, $pos, 9);
            $remainder = intval($chunk) % 97;
            $pos += 9;
        }

        if ($remainder !== 1) {
            $fail(__('validation.custom.iban.checksum'));
        }
    }
}
