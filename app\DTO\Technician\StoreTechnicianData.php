<?php

namespace App\DTO\Technician;

use <PERSON><PERSON>\LaravelData\Data;
use App\Enums\ApprovalStatus;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class StoreTechnicianData extends Data
{
    public function __construct(
        public ?string $email,
        public ?string $id_number,
        public ?int $nationality_id,
        public ?string $bank_account_number,
        public ?string $bank_name,
        public ?string $iban,
        public ?array $location,
        public ?UploadedFile $image,
    ) {
        //
    }

    /**
     * Extract fields that belong to the User model.
     */
    public function userData(): array
    {
        $data = array_filter([
            'email' => $this->email,
            'bank_account_number' => $this->bank_account_number,
            'bank_name' => $this->bank_name,
            'iban' => $this->iban,
            'image' => $this->image,
        ], fn ($value) => ! is_null($value));

        if ($this->location) {
            $data['location'] = new Point(
                $this->location['lat'],
                $this->location['lng']
            );
        }

        return $data;
    }

    /**
     * Extract fields that belong to RentalOperator model.
     */
    public function technicianData(): array
    {
        return [
            'user_id' => auth('user')->id(),
            'id_number' => $this->id_number,
            'nationality_id' => $this->nationality_id,
            'approval_status' => ApprovalStatus::PENDING,
        ];
    }
}
