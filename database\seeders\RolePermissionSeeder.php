<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // تفريغ الجداول قبل السييد
        // DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        // DB::table('role_has_permissions')->truncate();
        // DB::table('model_has_roles')->truncate();
        // DB::table('model_has_permissions')->truncate();
        // DB::table('permissions')->truncate();
        // DB::table('roles')->truncate();
        // DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        /**
         * Admin Panel Permissions
         */
        $adminPermissions = [
            'admins' => ['show admins', 'show admin', 'create admin', 'update admin', 'delete admin'],
            'users' => ['show users', 'show user', 'create user', 'update user', 'delete user'],
        ];

        foreach ($adminPermissions as $group => $actions) {
            foreach ($actions as $action) {
                Permission::firstOrCreate([
                    'name' => $action,
                    'group' => $group,
                    'guard_name' => 'admin', // ✅ guard admin
                ]);
            }
        }

        /**
         * Admin Roles
         */
        $superAdmin = Role::firstOrCreate([
            'name' => 'super admin',
            'guard_name' => 'admin', // ✅ guard admin
            'panel' => 'admin',
            'is_builtin' => true,
            'is_active' => true,
        ]);

        $moderator = Role::firstOrCreate([
            'name' => 'moderator',
            'guard_name' => 'admin', // ✅ guard admin
            'panel' => 'admin',
            'is_builtin' => true,
            'is_active' => true,
        ]);

        /**
         * Assign Permissions (Admin Panel)
         */
        $superAdmin->syncPermissions(Permission::all());

        $moderator->syncPermissions(
            Permission::whereNotIn('group', ['admins'])->get()
        );

        /**
         * Example: Vendor Panel (commented for demo)
         */
        /*
        $vendorPermissions = [
            'dishes' => ['show dishes', 'show dish', 'create dish', 'update dish', 'delete dish'],
            'offers' => ['show offers', 'show offer', 'create offer', 'update offer', 'delete offer'],
            'orders' => ['show vendor orders', 'show vendor order'],
        ];

        foreach ($vendorPermissions as $group => $actions) {
            foreach ($actions as $action) {
                Permission::firstOrCreate([
                    'name'       => $action,
                    'group'      => $group,
                    'guard_name' => 'vendor', // ✅ guard vendor
                ]);
            }
        }

        $vendorAdmin = Role::firstOrCreate([
            'name'       => 'vendor admin',
            'guard_name' => 'vendor', // ✅ guard vendor
            'panel'      => 'vendor',
            'is_builtin' => true,
            'is_active'  => true,
        ]);

        $vendorAdmin->syncPermissions(
            Permission::whereIn('group', ['dishes', 'offers', 'orders'])->get()
        );
        */
    }
}
