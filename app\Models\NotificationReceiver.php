<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationReceiver extends Model
{
    protected $fillable = [
        'group_notification_id',
        'receiverable_type',
        'receiverable_id',
    ];

    public function receiverable(): MorphTo
    {
        return $this->morphTo();
    }

    public function groupNotification(): BelongsTo
    {
        return $this->belongsTo(GroupNotification::class);
    }
}
