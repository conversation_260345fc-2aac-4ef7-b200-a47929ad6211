<?php

namespace App\Services\Provider;

use App\Repositories\UserRepository;
use App\Services\MediaUploaderService;
use Illuminate\Support\Facades\DB;
use MatanYadaev\EloquentSpatial\Objects\Point;

class ProfileService
{
    public function __construct(
        private readonly UserRepository $userRepository,
        private readonly MediaUploaderService $mediaService,
    ) {}

    public function update(array $data)
    {
        $user = auth('provider')->user();

        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['password'] = isset($data['password']) ? bcrypt($data['password']) : $user->password;
        $data['location_address'] = $data['location']['address'] ?? null;
        $data['location'] = isset($data['location']['lat']) ? new Point($data['location']['lat'], $data['location']['lng']) : null;

        if ($user->roles->count() == 0) {
            // remove shared data between roles
            $data_to_be_null = ['location', 'location_address', 'bank_name', 'bank_account_number', 'iban'];
            foreach ($data_to_be_null as $key) {
                $data[$key] = null;
            }

            $data['password'] = null;
        }

        DB::transaction(function () use ($user, $data) {
            $this->userRepository->update($user, $data);

            if (isset($data['image'])) {
                $user->clearMediaCollection();
                $this->mediaService->upload($user, $data['image']);
            }

            if ($user->supplier) {
                $user->supplier->update($data['supplier']);
                $user->supplier->categories()->sync($data['supplier']['categories']);

                if (isset($data['supplier']['company_image'])) {
                    $user->supplier->clearMediaCollection();
                    $this->mediaService->upload($user->supplier, $data['supplier']['company_image']);
                }
            }

            if ($user->technician) {
                $user->technician->update($data['technician']);
                $user->technician->specializations()->sync($data['technician']['specializations']);
            }
        });
    }
}
