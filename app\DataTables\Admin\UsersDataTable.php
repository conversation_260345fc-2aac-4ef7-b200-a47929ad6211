<?php

namespace App\DataTables\Admin;

use App\Models\User;
use Illuminate\Database\Eloquent\Builder as QueryBuilder;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Yajra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class UsersDataTable extends DataTable
{
    public function dataTable(QueryBuilder $query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->setRowId('id')
            ->addColumn('status', fn ($user): ?string => activeStatusBadge($user->is_active))
            ->addColumn('roles', fn ($user): ?string => count($user->roles) ? $user->roles_string : '-')
            ->addColumn('actions', 'pages.admin.users.actions')
            ->addColumn('phone', fn ($user): string => $user->phone)
            ->orderColumn('DT_RowIndex', function ($query, $direction): void {
                $query->orderBy('id', $direction);
            })
            ->rawColumns(['actions', 'phone', 'status'])
            ->addIndexColumn();
    }

    public function query(User $model): QueryBuilder
    {
        return $model->newQuery()
            ->with('supplier', 'technician', 'rentalOutlet')
            ->when(request('search_param'), function ($query): void {
                $query->where('name', 'LIKE', '%'.request('search_param').'%')
                    ->orWhere('username', 'LIKE', '%'.request('search_param').'%')
                    ->orWhere('phone', 'LIKE', '%'.request('search_param').'%');
            })
            ->when(request()->has('status') && request('status') != '', fn ($q) => $q->where('is_active', request('status')))
            ->when(request('type'), function ($query, $type) {
                if ($type == 'client') {
                    $query->whereDoesntHave('supplier')->whereDoesntHave('technician')->whereDoesntHave('rentalOutlet');
                } elseif (in_array($type, ['supplier', 'technician', 'rentalOutlet'])) {
                    $query->whereHas($type);
                }
            });
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('users-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'stateSave' => true,
                'stateDuration' => 10,
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    public function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::make('name')->title(__('Name'))->addClass('text-center')->orderable(false),
            Column::computed('phone')->title(__('Phone'))->addClass('text-center'),
            Column::computed('roles')->title(__('Role'))->addClass('text-center'),
            Column::computed('status')->title(__('Status'))->addClass('text-center'),
            Column::computed('actions')->title(__('Actions'))->addClass('text-center'),

        ];
    }

    protected function filename(): string
    {
        return 'Users_'.date('YmdHis');
    }
}
