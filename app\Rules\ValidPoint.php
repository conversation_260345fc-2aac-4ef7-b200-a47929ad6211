<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\DB;

class ValidPoint implements ValidationRule
{
    private string $latField;

    private string $lngField;

    public function __construct(string $latField, string $lngField)
    {
        $this->latField = $latField;
        $this->lngField = $lngField;
    }

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $lat = request()->input($this->latField);
        $lng = request()->input($this->lngField);

        if (is_array($lat)) {
            $lat = $lat[0];
        }
        if (is_array($lng)) {
            $lng = $lng[0];
        }

        if ($lat === null || $lng === null) {
            return; // other rules (required_with) will handle this
        }

        try {
            // Try constructing a POINT with SRID 4326
            DB::selectOne('SELECT ST_AsText(ST_GeomFromText(?, 4326)) as point', [
                "POINT($lng $lat)",
            ]);
        } catch (\Throwable $e) {
            $fail(__('The coordinates (:lat, :lng) are not valid geographic coordinates.', ['lat' => $lat, 'lng' => $lng]));
        }
    }
}
