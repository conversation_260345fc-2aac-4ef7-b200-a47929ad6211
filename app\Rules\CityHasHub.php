<?php

namespace App\Rules;

use App\Models\Hub;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CityHasHub implements ValidationRule
{
    public function __construct(
        private readonly bool $isExpress
    ) {}

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Skip validation if this is an express order
        if ($this->isExpress) {
            return;
        }

        // Check if the city has at least one hub
        $hasHub = Hub::where('city_id', $value)->exists();

        if (! $hasHub) {
            $fail(__('The selected city does not have any hubs available.'));
        }
    }
}
