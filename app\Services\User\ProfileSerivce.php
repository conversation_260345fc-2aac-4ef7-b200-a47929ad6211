<?php

namespace App\Services\User;

use App\DTO\User\UpdateProfileData;
use App\Repositories\UserRepository;
use App\Services\MediaUploaderService;
use Illuminate\Support\Arr;

class ProfileSerivce
{
    public function __construct(
        private readonly MediaUploaderService $mediaUploaderService,
        private readonly UserRepository $userRepository
    ) {
        //
    }

    public function update(UpdateProfileData $data): \App\Models\User
    {
        $user = auth()->user();

        $updateData = Arr::only(
            $data->all(),
            ['name', 'email', 'country_code', 'phone']
        );

        $user->update($updateData);

        if ($data->image instanceof \Symfony\Component\HttpFoundation\File\UploadedFile) {
            $this->mediaUploaderService->upload($user, $data->image, 'users', true, 'public');
        }

        return $user->refresh();
    }

    public function toggleBiometric(): bool
    {
        $user = auth()->user();
        $user->biometric_enabled = ! $user->biometric_enabled;
        $user->save();

        return $user->biometric_enabled;
    }

    public function toggleNotifications(): bool
    {
        $user = auth()->user();
        $user->notification_enabled = ! $user->notification_enabled;
        $user->save();

        return $user->notification_enabled;
    }

    public function deleteAccount(): void
    {
        $user = auth()->user();
        $this->userRepository->invalidateUniqueData($user);
        $user->currentAccessToken()->delete();
        $user->delete();
    }
}
