name: Deploy <PERSON><PERSON> App to cPanel

on:
  push:
    branches:
      - staging
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy to Staging
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v5

      - name: Deploy to Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_STAGING_HOST }}
          username: ${{ secrets.SSH_STAGING_USERNAME }}
          key: ${{ secrets.SSH_STAGING_PRIVATE_KEY }}
          port: ${{ secrets.SSH_STAGING_PORT }}
          script: |
            cd ${{ secrets.DEPLOY_PATH }}
            git pull origin staging
            composer install --no-dev --optimize-autoloader
            php artisan down
            php artisan migrate --force
            php artisan config:cache
            php artisan route:cache
            php artisan view:cache
            php artisan optimize
            php artisan up

