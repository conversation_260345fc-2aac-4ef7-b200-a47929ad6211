<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Session;

class SetLocale
{
    public function handle($request, Closure $next)
    {
        $locales = config('app.locales', ['en']); // fallback safety

        $locale = null;

        // If it's an API request → use Accept-Language header
        if ($request->is('api/*')) {
            $locale = $request->header('Accept-Language');
        }
        // If it's a Web request → use session
        elseif ($request->hasSession()) {
            $locale = Session::get('locale');
        }

        // Fallback: app.locale
        if (! $locale) {
            $locale = config('app.locale', 'en');
        }

        // Validate against supported locales
        if (! in_array($locale, $locales)) {
            $locale = config('app.fallback_locale', 'en');
        }

        // Apply
        App::setLocale($locale);
        Config::set('app.locale', $locale);
        Carbon::setLocale($locale);

        return $next($request);
    }
}
