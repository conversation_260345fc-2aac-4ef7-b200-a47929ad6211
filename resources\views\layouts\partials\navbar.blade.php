@php
    $guard = request()->routeIs('admin.*') ? 'admin' : 'provider';
@endphp

<nav class="layout-navbar navbar navbar-expand-xl navbar-detached align-items-center bg-navbar-theme" id="layout-navbar">
    <div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0 d-xl-none">
        <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
            <i class="ti ti-menu-2 ti-sm"></i>
        </a>
    </div>

    <div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
        <ul class="navbar-nav flex-row align-items-center ms-auto">

            <li class="nav-item me-3">
                <div class="dropdown">
                    <button class="btn btn-ghost-secondary dropdown-toggle p-0 border-0" type="button"
                        id="langDropdown" data-bs-toggle="dropdown" aria-expanded="false"
                        style="width: 48px; height: 48px;">
                        @php
                            $currentLocale = app()->getLocale();
                            $flagClass = $currentLocale === 'ar' ? 'fi-sa' : 'fi-us';
                        @endphp
                        <span class="fi {{ $flagClass }}"></span>
                    </button>

                    <ul class="dropdown-menu dropdown-menu-end shadow-sm border-0 rounded-3 py-1"
                        aria-labelledby="langDropdown" style="min-width: auto; width: 100px;">
                        <li>
                            <a class="dropdown-item d-flex align-items-center gap-2 p-2 @if ($currentLocale == 'en') active @endif"
                                href="{{ route('locale.switch', ['locale' => 'en']) }}">
                                <span class="fi fi-us"></span>
                                EN
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item d-flex align-items-center gap-2 p-2 @if ($currentLocale == 'ar') active @endif"
                                href="{{ route('locale.switch', ['locale' => 'ar']) }}">
                                <span class="fi fi-sa"></span>
                                AR
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

            <!-- Notification -->
            <li class="nav-item me-3">
                @include('partials.notifications')
            </li>

            <!-- User -->
            <li class="nav-item navbar-dropdown dropdown-user dropdown">
                <a class="nav-link dropdown-toggle hide-arrow d-flex align-items-center" href="javascript:void(0);"
                    data-bs-toggle="dropdown">
                    {{-- Avatar --}}
                    <div class="avatar avatar-online me-2">
                        <img src="{{ auth($guard)->user()->imageUrl ?: url('user-default.png') }}"
                            class="rounded-circle" />
                    </div>

                    {{-- Name + Email stacked --}}
                    <div class="d-flex flex-column text-start">
                        <span class="fw-medium">
                            {{ auth($guard)->user()->name }}
                        </span>
                        <small class="text-muted">
                            {{ auth($guard)->user()->email }}
                        </small>
                    </div>
                </a>

                <ul class="dropdown-menu dropdown-menu-end text-end">
                    {{-- Profile --}}
                    <li>
                        <a class="dropdown-item d-flex align-items-center justify-content-between"
                            href="{{ route($guard . '.profile') }}">
                            <span>{{ __('Profile') }}</span>
                            <i class="ti ti-user"></i>
                        </a>
                    </li>

                    {{-- Dark mode toggle --}}
                    <li>
                        <div class="dropdown-item d-flex align-items-center justify-content-between">
                            <span>{{ __('Dark Mode') }}</span>
                            <div class="form-check form-switch m-0">
                                <input class="form-check-input" type="checkbox" id="darkModeSwitch">
                            </div>
                            <i class="ti ti-moon"></i>
                        </div>
                    </li>

                    <li>
                        <div class="dropdown-divider"></div>
                    </li>

                    {{-- Logout --}}
                    <li>
                        <a class="dropdown-item d-flex align-items-center justify-content-between text-danger"
                            href="{{ route($guard . '.logout') }}">
                            <span>{{ __('Logout') }}</span>
                            <i class="ti ti-logout"></i>
                        </a>
                    </li>
                </ul>
            </li>

            @push('js')
                <script>
                    document.addEventListener("DOMContentLoaded", function() {
                        const body = document.body;
                        const toggle = document.getElementById("darkModeToggle");

                        if (localStorage.getItem("darkMode") === "enabled") {
                            body.classList.add("dark-mode");
                        }

                        toggle.addEventListener("click", function() {
                            body.classList.toggle("dark-mode");

                            if (body.classList.contains("dark-mode")) {
                                localStorage.setItem("darkMode", "enabled");
                            } else {
                                localStorage.setItem("darkMode", "disabled");
                            }
                        });
                    });
                </script>
            @endpush
        </ul>
    </div>
</nav>
