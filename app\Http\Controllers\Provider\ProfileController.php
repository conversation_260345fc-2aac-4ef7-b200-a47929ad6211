<?php

namespace App\Http\Controllers\Provider;

use App\Http\Controllers\Controller;
use App\Http\Requests\Provider\ProfileRequest;
use App\Repositories\CategoryRepository;
use App\Repositories\CountryRepository;
use App\Repositories\SpecializationRepository;
use App\Services\Provider\ProfileService;

class ProfileController extends Controller
{
    public function __construct(
        private ProfileService $profileService,
        private readonly CountryRepository $countryRepository,
        private readonly SpecializationRepository $specializationRepository,
        private readonly CategoryRepository $categoryRepository,
    ) {}

    public function index()
    {
        $user = auth('provider')->user();
        $countries = $this->countryRepository->getForSelect();
        $specializations = $this->specializationRepository->all();
        $categories = $this->categoryRepository->all();

        return view('pages.provider.profile.index', compact('user', 'countries', 'specializations', 'categories'));
    }

    public function update(ProfileRequest $request)
    {
        // return $request->all();
        $this->profileService->update($request->validated());

        return back()->with('success', __('Profile updated successfully'));
    }
}
