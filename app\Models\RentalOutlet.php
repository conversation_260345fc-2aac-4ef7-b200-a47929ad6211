<?php

namespace App\Models;

use App\Enums\ApprovalStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class RentalOutlet extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'approval_status',
    ];

    public function casts(): array
    {
        return [
            'approval_status' => ApprovalStatus::class,
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
