<?php

namespace App\Rules;

use App\Models\Address;
use App\Repositories\CityRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * For intercity shipping, ensure dropoff_city_id differs from pickup city when not express.
 */
class IntercityDropoffCityDifferentFromPickup implements ValidationRule
{
    public function __construct(private readonly int $shippingTypeId, private readonly bool $isExpress) {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Only applies to type 2 (intercity) and when not express
        if ((int) $this->shippingTypeId !== 2 || $this->isExpress) {
            return;
        }

        $dropoffCityId = $value;
        if (! $dropoffCityId) {
            return;
        }

        $pickupCityId = $this->resolvePickupCityId();
        if ($pickupCityId && (int) $dropoffCityId === (int) $pickupCityId) {
            $fail(__('validation.different', ['attribute' => __('attributes.dropoff_city_id'), 'other' => __('attributes.pickup_city_id')]));
        }
    }

    private function resolvePickupCityId(): ?int
    {
        $pickupAddressId = request('pickup_address_id');
        if ($pickupAddressId) {
            $address = Address::query()->with('area:id,city_id')->find($pickupAddressId);

            return $address?->area?->city_id;
        }

        $lat = request('pickup_location.lat');
        $lng = request('pickup_location.lng');
        if ($lat && $lng) {
            $city = app(CityRepository::class)->getByLocation((float) $lat, (float) $lng);

            return $city?->id;
        }

        return null;
    }
}
