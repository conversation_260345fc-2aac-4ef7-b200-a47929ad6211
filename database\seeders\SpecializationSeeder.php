<?php

namespace Database\Seeders;

use App\Models\Specialization;
use Illuminate\Database\Seeder;

class SpecializationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $specializations = [
            ['en' => 'Plumbing', 'ar' => 'السباكة'],
            ['en' => 'Electrical Works', 'ar' => 'الأعمال الكهربائية'],
            ['en' => 'Painting & Finishing', 'ar' => 'الدهانات والتشطيبات'],
            ['en' => 'Carpentry', 'ar' => 'النجارة'],
            ['en' => 'Masonry & Tiling', 'ar' => 'البناء وتركيب البلاط'],
            ['en' => 'Air Conditioning', 'ar' => 'التكييف'],
            ['en' => 'Roofing', 'ar' => 'تسقيف'],
        ];

        foreach ($specializations as $name) {
            Specialization::create([
                'name' => $name,
            ]);
        }
    }
}
