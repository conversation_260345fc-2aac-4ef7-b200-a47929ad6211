<?php

namespace App\Http\Controllers\Api\User;

use App\DTO\Supplier\UpgradeSupplierData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\Supplier\StoreRequest;
use App\Services\User\SupplierService;

class SupplierController extends Controller
{
    public function __construct(private readonly SupplierService $supplierService)
    {
        //
    }

    public function store(StoreRequest $request)
    {
        $dto = UpgradeSupplierData::from($request->validated());
        $this->supplierService->create($dto);

        return success(__('Your account upgrade request has been sent successfully.'));
    }
}
