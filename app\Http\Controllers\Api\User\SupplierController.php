<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Services\User\SupplierService;
use Illuminate\Http\Request;
use App\DTO\Supplier\UpgradeSupplierData;

class SupplierController extends Controller
{
    public function __construct(
        protected SupplierService $supplierService
    ) {}


    public function upgrade(UpgradeSupplierData $data)
    {
        $user = auth()->user();
        $this->supplierService->upgradeUserToSupplier($user, $data);

        return success(__('Your request is under review.'));
    }

}
