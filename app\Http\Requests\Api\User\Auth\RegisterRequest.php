<?php

namespace App\Http\Requests\Api\User\Auth;

use App\Models\User;
use App\Rules\UniquePhone;
use App\Rules\ValidPhone;
use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:3', 'max:30'],
            'email' => [
                'nullable',
                'string',
                'email:rfc,dns',
                'unique:users,email',
            ],
            'country_code' => ['required_with:phone', 'required', 'regex:/^\d{1,4}$/'],
            'phone' => [
                'required_with:country_code',
                'required',
                'string',
                new UniquePhone($this->input('country_code'), User::class),
                new ValidPhone($this->input('country_code')),
            ],
        ];
    }
}
