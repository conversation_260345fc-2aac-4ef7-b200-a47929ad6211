<?php

namespace App\DataTables\Admin;

use App\Models\GroupNotification;
use Ya<PERSON>ra\DataTables\EloquentDataTable;
use Ya<PERSON>ra\DataTables\Html\Builder as HtmlBuilder;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class GroupNotificationDataTable extends DataTable
{
    public function dataTable($query): EloquentDataTable
    {
        return (new EloquentDataTable($query))
            ->addColumn('actions', 'pages.admin.notifications.actions')
            ->editColumn('title', function (GroupNotification $notification) {
                return $notification->getTranslation('title', app()->getLocale());
            })
            ->editColumn('sender', fn (GroupNotification $notification) => $notification->sender?->name ?? '-')
            ->editColumn('recipients_count', fn (GroupNotification $notification) => $notification->receivers_count)
            ->orderColumn('DT_RowIndex', function ($query, $direction): void {
                $query->orderBy('id', $direction);
            })
            ->rawColumns(['actions'])
            ->addIndexColumn();
    }

    public function query(GroupNotification $model)
    {
        return $model->newQuery()
            ->with('sender')
            ->withCount('receivers');
    }

    public function html(): HtmlBuilder
    {
        return $this->builder()
            ->setTableId('notifications-table')
            ->columns($this->getColumns())
            ->orderBy(0, 'desc')
            ->minifiedAjax()
            ->searching(false)
            ->parameters([
                'stateSave' => true,
                'stateDuration' => 10,
                'language' => [
                    'url' => asset('assets/json/datatable-translations/'.app()->getLocale().'.json'),
                ],
            ]);
    }

    protected function getColumns(): array
    {
        return [
            Column::make('DT_RowIndex')->title('#')->addClass('text-center')->searchable(false),
            Column::make('title')->title(__('Title')),
            Column::make('sender')->title(__('Sender')),
            Column::make('type')->title(__('Type')),
            Column::make('recipients_count')->title(__('Recipients'))->sortable(false),
            Column::make('created_at')->title(__('Created At'))->format('Y-m-d H:i'),
            Column::computed('actions')->title(__('Actions'))->exportable(false)->printable(false)->addClass('text-center'),
        ];
    }
}
