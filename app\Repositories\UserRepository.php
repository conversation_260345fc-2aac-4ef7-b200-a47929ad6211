<?php

namespace App\Repositories;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use MatanYadaev\EloquentSpatial\Objects\Point;

class UserRepository
{
    public function __construct(private readonly User $model)
    {
        //
    }

    public function findByEmailOrUsername(string $value)
    {
        return $this->model->where('email', $value)->orWhere('username', $value)->first();
    }

    public function findByEmail(string $email): ?User
    {
        return $this->model->where('email', $email)->first();
    }

    public function getByPhone(string $phone)
    {
        return $this->model->whereIn('phone', [$phone, "0$phone", substr($phone, 1)])->first();
    }

    public function findByPhoneWithCountryCode(string $phone, string $countryCode): ?User
    {
        return $this->model->where('phone', normalizePhoneNumber($phone))
            ->where('country_code', $countryCode)
            ->first();
    }

    public function findByIdentifier(string $identifier, ?string $countryCode = null): ?User
    {
        if (filter_var($identifier, FILTER_VALIDATE_EMAIL)) {
            return $this->findByEmail($identifier);
        }

        if ($countryCode) {
            return $this->findByPhoneWithCountryCode(normalizePhoneNumber($identifier), $countryCode);
        }

        return $this->getByPhone(normalizePhoneNumber($identifier));
    }

    public function createGuest(): User
    {
        return $this->model->create([
            'guest_token' => Str::uuid(),
        ]);
    }

    /**
     * Merge guest account into regular user account
     * This method handles the migration of guest data to a regular user account
     */
    public function mergeGuestAccount(User $guestUser, User $regularUser): void
    {
        // Transfer guest-specific data to regular user
        $this->transferGuestData($guestUser, $regularUser);

        // Delete the guest account
        $guestUser->delete();
    }

    /**
     * Transfer guest data to regular user account
     * This method is extensible for future features like cart, orders, etc.
     */
    protected function transferGuestData(User $guestUser, User $regularUser): void
    {
        // This method can be extended for:
        // - Cart items
        // - Order history
        // - Wishlist items
        // - User preferences
        // - Any other guest-specific data

        // Example implementation for future features:
        // if (class_exists('App\Models\Cart')) {
        //     $guestUser->cart()->update(['user_id' => $regularUser->id]);
        // }
        //
        // if (class_exists('App\Models\Order')) {
        //     $guestUser->orders()->update(['user_id' => $regularUser->id]);
        // }
    }

    /**
     * Find guest user by guest token
     */
    public function findGuestByToken(string $guestToken): ?User
    {
        return $this->model->where('guest_token', $guestToken)->first();
    }

    public function create(array $data): User
    {
        return $this->model->create($data);
    }

    public function createSocial(object $socialUser, string $provider): User
    {
        $user = $this->model->create([
            'name' => trim(($socialUser->user['name'] ?? (($socialUser->user['given_name'] ?? '') . ' ' . ($socialUser->user['family_name'] ?? '')))) ?: null,
            'email' => $socialUser->email,
            'email_verified_at' => now(),
        ]);

        $user->socialCredentials()->create([
            'provider' => $provider,
            'provider_id' => $socialUser->id,
        ]);

        return $user;
    }

    public function findByBiometricToken(string $biometricToken): ?User
    {
        return $this->model->get()->first(function ($user) use ($biometricToken) {
            return Hash::check($biometricToken, $user->biometric_token);
        });
    }

    public function invalidateUniqueData(User $user): void
    {
        $user->update([
            'phone' => getInvalidatedValue($user->phone),
            'email' => getInvalidatedValue($user->email),
            'username' => getInvalidatedValue($user->username),
        ]);
    }

    public function updateData(User $user, array $data): User
    {
        $filteredData = array_filter($data, fn($value) => $value !== null);

        if (isset($filteredData['image'])) {
            $user->clearMediaCollection('users');
            $user->addMedia($filteredData['image'])->toMediaCollection('users');
            unset($filteredData['image']);
        }

        if (isset($filteredData['location']) && is_array($filteredData['location'])) {
            $filteredData['location'] = new Point(
                $filteredData['location']['lat'],
                $filteredData['location']['lng']
            );
        }

        $user->update($filteredData);

        return $user->fresh();
    }
}
