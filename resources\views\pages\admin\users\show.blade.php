<x-layout :title="__('User Info')">
    <x-session-message />

    <div class="card p-4">

        <div class="mb-4">
            <h6 class="fw-bold text-start">{{ __('User Info') }}</h6>
        </div>

        <!-- Profile Image -->
        <div class="text-center mb-3">
            <img src="{{ $user->image_url }}"
                alt="User Avatar"
                class="rounded-circle"
                width="120"
                height="120">
        </div>

        <!-- Divider -->
        <hr>

        <!-- Info Grid -->
        <div class="row mt-4 g-3 align-items-stretch">
            <!-- User Basic Info -->
            <div class="col-md-4 mb-3">
                <x-info-item icon="user" label="{{ __('Name') }}" :value="$user->name" />
            </div>
            <div class="col-md-4 mb-3">
                <x-info-item icon="phone" label="{{ __('Phone') }}" :value="$user->phone" />
            </div>
            <div class="col-md-4 mb-3">
                <x-info-item icon="mail" label="{{ __('Email') }}" :value="$user->email" />
            </div>

            <!-- only if user has any role -->
            @if($user->roles->count())
            <div class="col-md-4 mb-3">
                <x-info-item icon="building-bank" label="{{ __('Bank Name') }}" :value="$user->bank_name" />
            </div>
            <div class="col-md-4 mb-3">
                <x-info-item icon="credit-card" label="{{ __('Bank Account Number') }}" :value="$user->bank_account_number" />
            </div>
            <div class="col-md-4 mb-3">
                <x-info-item icon="barcode" label="{{ __('IBAN') }}" :value="$user->iban" />
            </div>

            <!-- Location Address -->
            <div class="col-md-4 mb-3">
                <x-info-item icon="map-pin" label="{{ __('Location') }}" :value="$user->location_address" />
            </div>
            @endif

            <!-- Supplier Info -->
            @if($user->supplier)
            <div class="col-md-4 mb-3">
                <x-info-item icon="building" label="{{ __('Company Name') }}" :value="$user->supplier->company_name" />
            </div>
            <div class="col-md-4 mb-3">
                <x-info-item icon="mail" label="{{ __('Company Email') }}" :value="$user->supplier->company_email" />
            </div>
            <div class="col-md-4 mb-3">
                <x-info-item icon="meteor" label="{{ __('Supplier Type') }}" :value="__($user->supplier->type)" />
            </div>
            <div class="col-md-4 mb-3">
                <x-info-item icon="id" label="{{ __('Commercial Register') }}" :value="$user->supplier->commercial_register" />
            </div>
            <div class="col-md-4 mb-3">
                <x-info-item icon="receipt-tax" label="{{ __('Tax Number') }}" :value="$user->supplier->tax_number" />
            </div>

            <div class="col-md-4 mb-3">
                <x-info-item icon="category" label="{{ __('Supplier Specializations') }}" :value="$user->supplier->categories->pluck('name')->implode(' - ')" />
            </div>
            @endif

            <!-- Technician Info -->
            @if($user->technician)
            <div class="col-md-4 mb-3">
                <x-info-item icon="id-badge" label="{{ __('ID Number') }}" :value="$user->technician->id_number" />
            </div>
            <div class="col-md-4 mb-3">
                <x-info-item icon="flag" label="{{ __('Nationality') }}" :value="$user->technician->nationality->translated_name ?? '-'" />
            </div>
            <div class="col-md-4 mb-3">
                <x-info-item icon="tool" label="{{ __('Technician Specializations') }}" :value="$user->technician->specializations->pluck('name')->implode(' - ')" />
            </div>
            @endif
        </div>

        <!-- Actions -->
        <div class="d-flex justify-content-end mt-4 gap-3">
            @can('update user')
            <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-primary">
                {{ __('Edit') }}
            </a>
            @endcan

            @can('delete user')
            <a href="javascript:void(0)" class="btn btn-light border"
                data-bs-toggle="modal"
                data-bs-target="#delete-modal"
                onclick="changeDeleteModalData(this)"
                delete-route="{{ route('admin.users.destroy', $user) }}"
                delete-name="{{ __('User') }} : {{ $user->name }}">
                {{ __('Delete') }}
            </a>

            <x-delete-modal />
            @endcan

        </div>

    </div>
</x-layout>