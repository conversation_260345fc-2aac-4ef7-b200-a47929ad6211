body {
    background-color: #F4F5F6 !important;
}

nav#layout-navbar {
    background: transparent !important;
    box-shadow: none;
    border-radius: unset;
    border-bottom: 1px solid #E5E5E5 !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

.card-header {
    font-size: 22px !important;
}

.card-heading {
    display: flex;
    justify-content: space-between;
    font-size: 22px !important;
    font-weight: 500 !important;
    padding: 25px
}

.ti {
    font-size: 1.60rem;
}

td .actions {
    display: flex;
    justify-content: center;
    gap: 20px;
}

table i.ti-eye {
    color: #3ea0e3;
}

table i.ti-edit {
    color: #222222;
}

table i.ti-archive {
    color: #dc3545;
}

.card-image {
    width: 200px;
    height: 150px;
    border: 1px solid #ccc;
    border-radius: 3px
}

.modal-dialog {
    top: 30%;
}

textarea {
    overflow: hidden;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

textarea::-webkit-scrollbar {
    display: none;
}

#filters {
    padding: 0 30px;
}

.swal2-container {
    z-index: 99999 !important;
}

.swal2-timer-progress-bar {
    background-color: #fff !important;
}

.bg-menu-theme {
    background-color: #2C3E50 !important;
    color: #fff !important;
}

.bg-menu-theme .menu-link,
.bg-menu-theme .menu-horizontal-prev,
.bg-menu-theme .menu-horizontal-next {
    color: #fff !important;
}

.app-brand {
    height: 175px !important;
    justify-content: center !important;
}

.app-brand img {
    object-fit: none;
}

html:not(.layout-menu-collapsed) .bg-menu-theme .menu-inner .menu-item:not(.active)>.menu-link:hover,
.layout-menu-hover.layout-menu-collapsed .bg-menu-theme .menu-inner .menu-item:not(.active)>.menu-link:hover {
    background-color: transparent !important;
    color: #E57F25 !important;
}

html:not(.layout-menu-collapsed) .bg-menu-theme .menu-inner .menu-item:not(.active)>.menu-link.active,
.layout-menu-a.active.layout-menu-collapsed .bg-menu-theme .menu-inner .menu-item:not(.active)>.menu-link.active {
    background-color: transparent !important;
    color: #E57F25 !important;
}

.menu-inner-shadow {
    color: #fff !important;
    position: static !important;
    background: #fff !important;
    display: block;
    height: 1px;
    margin: auto;
    width: 70%;
    opacity: 20%;
}

button#notificationDropdown::after {
    display: none;
}

button#notificationDropdown,
button#langDropdown {
    background-color: #fff;
    width: 65px !important;
    height: 42px !important;
    display: flex;
    justify-content: center;
}