<?php

namespace App\Services\User;

use App\DTO\User\GuestLoginData;
use App\Http\Resources\User\GuestResource;
use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class GuestAuthService
{
    public function __construct(
        private readonly UserRepository $userRepository
    ) {
        //
    }

    /**
     * Create or find guest user
     */
    public function login(GuestLoginData $data): array
    {
        $guest = $this->userRepository->createGuest();

        if ($data->device_id) {
            $guest->updateDeviceToken([
                'device_id' => $data->device_id,
                'firebase_token' => $data->firebase_token,
            ]);
        }

        return [
            'user' => new GuestResource($guest),
            'token' => $guest->createToken('guest-token')->plainTextToken,
        ];
    }

    public function mergeGuestIntoUser(string $guestToken, User $user): void
    {
        DB::transaction(function () use ($guestToken, $user): void {
            $guest = $this->userRepository->findGuestByToken($guestToken);

            if (! $guest instanceof \App\Models\User) {
                throw new NotFoundHttpException(__('Guest user not found.'));
            }

            if ($guest->id === $user->id) {
                return; // Prevent self-merging
            }

            $this->userRepository->mergeGuestAccount($guest, $user);
        });
    }
}
