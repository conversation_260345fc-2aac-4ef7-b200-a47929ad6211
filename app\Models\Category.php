<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Category extends Model
{
    use HasTranslations;

    protected $fillable = [
        'name',
        'type',
        'parent_id',
    ];

    public $translatable = ['name'];

    public function scopeForSelect(Builder $query)
    {
        $query->where('type', 'main')->whereHas('children');
    }

    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function suppliers()
    {
        return $this->belongsToMany(Supplier::class, 'supplier_category');
    }
}
