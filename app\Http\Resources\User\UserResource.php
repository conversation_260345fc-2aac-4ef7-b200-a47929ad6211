<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'country_code' => $this->country_code,
            'phone' => $this->phone,
            'image' => $this->getFirstMediaUrl('users') ?: null,
            'biometric_token' => $this->biometric_token,
            'biometric_enabled' => $this->biometric_enabled,
            'notification_enabled' => $this->notification_enabled,
            'is_technician' => (bool) false,
            'is_supplier' => (bool) false,
            'is_rental_support' => (bool) false,
            'username' => $this->when($this->username, $this->username),
            'location' => $this->when($this->location, function () {
                return [
                    'lat' => (string) $this->location->latitude,
                    'lng' => (string) $this->location->longitude,
                ];
            }),
            'bank_account_number' => $this->when($this->bank_account_number, $this->bank_account_number),
            'bank_name' => $this->when($this->bank_name, $this->bank_name),
            'iban' => $this->when($this->iban, $this->iban),
        ];
    }
}
