<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Specialization extends Model
{
    use HasTranslations;

    protected $fillable = [
        'name',
        'is_active',
    ];

    public $translatable = ['name'];

    public function casts(): array
    {
        return [
            'is_active' => 'boolean',
        ];
    }

    public function scopeActive(Builder $query)
    {
        $query->where('is_active', true);
    }

    public function technicians()
    {
        return $this->belongsToMany(Technician::class, 'technician_specialization');
    }
}
