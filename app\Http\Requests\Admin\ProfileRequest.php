<?php

namespace App\Http\Requests\Admin;

use App\Models\Admin;
use App\Rules\UniquePhone;
use App\Rules\ValidMedia;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'min:1', 'max:30'],
            'country_code' => ['required', 'string', 'exists:countries,code'],
            'phone' => ['required', 'string', new ValidPhone($this->country_code), new UniquePhone($this->country_code, Admin::class, auth('admin')->id())],
            'email' => ['required', 'string', 'email:rfc,dns,filter', 'max:255', Rule::unique('admins', 'email')->ignore(auth('admin')->id())],
            'current_password' => ['nullable', 'required_with:password', 'current_password:admin'],
            'password' => ['nullable', 'string',  new ValidPassword, 'confirmed'],
            'image' => ['nullable', new ValidMedia(['image'])],
        ];
    }
}
