<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseFormRequest;
use App\Models\Admin;
use App\Rules\UniquePhone;
use App\Rules\ValidMedia;
use App\Rules\ValidPassword;
use App\Rules\ValidPhone;
use Illuminate\Validation\Rule;

class ProfileRequest extends BaseFormRequest
{
    public function rules(): array
    {
        $admin = auth('admin')->user();

        return [
            'name' => ['required', 'string', 'min:4', 'max:30'],
            'username' => ['required', 'string', 'regex:/^[A-Za-z][A-Za-z0-9_]*$/', 'min:5', 'max:15', Rule::unique('admins', 'username')->ignore($admin->id)],
            'phone' => ['required', 'string', new ValidPhone('966'), new UniquePhone('966', Admin::class, $admin->id)],
            'email' => ['required', 'string', 'email:rfc,dns', 'max:255', Rule::unique('admins', 'email')->ignore($admin->id)],
            'image' => ['nullable', new ValidMedia(['image'])],
            'current_password' => ['nullable', 'required_with:password', 'current_password:admin'],
            'password' => ['nullable', 'string',  new ValidPassword, 'confirmed'],
        ];
    }
}
