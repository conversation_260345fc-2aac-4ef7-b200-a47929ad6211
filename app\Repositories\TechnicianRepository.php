<?php

namespace App\Repositories;

use App\Models\Technician;

class TechnicianRepository
{
    public function __construct(private readonly Technician $model)
    {
        //
    }

    /**
     * Find a technician by user ID.
     */
    public function findByUserId(int $userId): ?Technician
    {
        return $this->model->where('user_id', $userId)->first();
    }

    /**
     * Create a new technician.
     */
    public function create(array $data): Technician
    {
        $technician = $this->model->create($data);

        if (isset($data['specializations'])) {
            $technician->specializations()->sync($data['specializations']);
        }

        return $technician;
    }
}
