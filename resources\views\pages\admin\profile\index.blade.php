<x-layout :title="__('Edit Profile')">
    <x-session-message />
    <div class="card">
        <div class="card-header">{{ __('Edit Profile') }}</div>
        <div class="card-body">
            <form action="{{ route('admin.profile') }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="row">
                    <!-- Name -->
                    <div class="mb-3 col-lg-4">
                        <label for="name" class="form-label"><b>{{ __('Name') }}</b></label>
                        <input type="text" name="name" id="name" class="form-control"
                            value="{{ old('name', $admin->name) }}">
                        <x-input-error name="name" />
                    </div>

                    <!-- Phone -->
                    <div class="mb-3 col-lg-4">
                        <label for="phone" class="form-label"><b>{{ __('Phone') }}</b></label>
                        <input type="number" name="phone" id="phone" class="form-control"
                            value="{{ old('phone', $admin->phone) }}">
                        <x-input-error name="phone" />
                    </div>

                    <!-- Email -->
                    <div class="mb-3 col-lg-4">
                        <label for="email" class="form-label"><b>{{ __('Email') }}</b></label>
                        <input type="text" name="email" id="email" class="form-control"
                            value="{{ old('email', $admin->email) }}">
                        <x-input-error name="email" />
                    </div>

                    <!-- Username -->
                    <div class="mb-3 col-lg-4">
                        <label for="username" class="form-label"><b>{{ __('Username') }}</b></label>
                        <input type="text" name="username" id="username" class="form-control"
                            value="{{ old('username', $admin->username) }}">
                        <x-input-error name="username" />
                    </div>
                </div>

                <div class="row">
                    <div class="mb-3 col-lg-4">
                        <label for="current_password" class="form-label"><b>{{ __('Current Password') }}</b></label>
                        <input type="password" name="current_password" class="form-control" id="current_password" autocomplete="off">
                        <x-input-error name="current_password" />
                    </div>
                </div>

                <div class="row">
                    <div class="mb-3 col-lg-4">
                        <label for="password" class="form-label"><b>{{ __('New Password') }}</b></label>
                        <input type="password" name="password" class="form-control" id="password" autocomplete="new-password">
                        <x-input-error name="password" />
                    </div>
                    <div class="mb-3 col-lg-4">
                        <label for="password_confirmation" class="form-label"><b>{{ __('Password Confirmation') }}</b></label>
                        <input type="password" name="password_confirmation" class="form-control" id="password_confirmation" autocomplete="new-password">
                        <x-input-error name="password_confirmation" />
                    </div>
                </div>

                <!-- Image -->
                <div class="row">
                    <div class="mb-4 col-lg-4">
                        <label for="formFile" class="form-label"><b>{{ __('Image') }}</b></label>
                        <input name="image" class="form-control" type="file" id="formFile">
                        @if($admin->image_url)
                        <a href="{{ $admin->image_url }}">
                            <img src="{{ $admin->image_url }}" alt="User Image" class="mt-2 rounded" width="100">
                        </a>
                        @endif
                        <x-input-error name="image" />
                    </div>

                </div>

                <div class="d-flex justify-content-end gap-3 mt-4">
                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                    <a href="{{ route('admin.index') }}" class="btn btn-outline-primary">{{ __('Back') }}</a>
                </div>
            </form>
        </div>
    </div>
</x-layout>