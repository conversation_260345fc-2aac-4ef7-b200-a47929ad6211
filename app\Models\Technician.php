<?php

namespace App\Models;

use App\Enums\ApprovalStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Technician extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'id_number',
        'nationality_id',
        'approval_status',
    ];

    public function casts(): array
    {
        return [
            'approval_status' => ApprovalStatus::class,
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function nationality()
    {
        return $this->belongsTo(Country::class, 'nationality_id');
    }

    public function specializations()
    {
        return $this->belongsToMany(Specialization::class, 'technician_specialization');
    }
}
