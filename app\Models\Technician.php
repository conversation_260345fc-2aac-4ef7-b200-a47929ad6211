<?php

namespace App\Models;

use App\Enums\ApprovalStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Technician extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'id_number',
        'nationality_id',
        'approval_status',
    ];

    public function casts(): array
    {
        return [
            'approval_status' => ApprovalStatus::class,
        ];
    }

    public function getIdNumberAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function nationality()
    {
        return $this->belongsTo(Country::class, 'nationality_id');
    }

    public function specializations()
    {
        return $this->belongsToMany(Specialization::class, 'technician_specialization');
    }
}
