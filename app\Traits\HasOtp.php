<?php

namespace App\Traits;

use App\Mail\OtpMail;
use Illuminate\Foundation\Auth\User;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;
use InvalidArgumentException;

trait HasOtp
{
    /**
     * Generate and send OTP for the given user
     */
    protected function generateAndSendOtp(User $user, string $type, string $value, ?string $countryCode = null): string
    {
        $this->validateOtpRateLimit($type, $value, $countryCode);

        $code = $this->generateUniqueOtpCode();

        $user->otps()->updateOrCreate(
            [],
            [
                'code' => $code,
                'expires_at' => now()->addMinutes(5),
            ]
        );

        $this->dispatchOtp($type, $value, $code);

        return __('Verification code has been sent successfully.');
    }

    /**
     * Verify OTP for the given user
     */
    protected function verifyOtp(User $user, string $type, string $value, string $otpCode, ?string $countryCode = null): bool
    {
        $this->validateOtpVerificationRateLimit($type, $value);

        $otp = $user->otps()
            ->where('code', $otpCode)
            ->where('expires_at', '>', now())
            ->latest()
            ->first();

        if (! $otp) {
            return false;
        }

        $otp->delete();

        return true;
    }

    /**
     * Validate OTP sending rate limits
     */
    protected function validateOtpRateLimit(string $type, string $value, ?string $countryCode = null): void
    {
        // Long-term key (3 attempts in 12 hours)
        $longKey = "send-otp:long:{$type}:{$value}";
        // Short-term key (1 minute wait between sends)
        $shortKey = "send-otp:short:{$type}:{$value}";

        if ($type === 'phone' && $countryCode) {
            $longKey .= ':' . $countryCode;
            $shortKey .= ':' . $countryCode;
        }

        // 1. First check long-term (3 attempts / 12h)
        if (RateLimiter::tooManyAttempts($longKey, 3)) {
            $seconds = RateLimiter::availableIn($longKey);
            throw ValidationException::withMessages([
                'otp' => [__('Too many attempts. Please wait :seconds before retrying.', [
                    'seconds' => gmdate('H:i:s', $seconds),
                ])],
            ]);
        }

        // 2. Then check short-term (1/minute)
        if (RateLimiter::tooManyAttempts($shortKey, 1)) {
            $seconds = RateLimiter::availableIn($shortKey);
            throw ValidationException::withMessages([
                'otp' => [__('Please wait :seconds before resending the OTP.', [
                    'seconds' => gmdate('i:s', $seconds),
                ])],
            ]);
        }

        // Apply both limits
        RateLimiter::hit($shortKey, 60); // 1 minute
        RateLimiter::hit($longKey, 60 * 60 * 12); // 12 hours
    }

    /**
     * Validate OTP verification rate limits
     */
    protected function validateOtpVerificationRateLimit(string $type, string $value): void
    {
        $key = "verify-otp:{$type}:{$value}";

        if (RateLimiter::tooManyAttempts($key, 3)) {
            $seconds = RateLimiter::availableIn($key);
            throw ValidationException::withMessages([
                'otp' => [__('Too many attempts. Please wait :seconds before retrying.', [
                    'seconds' => gmdate('H:i:s', $seconds),
                ])],
            ]);
        }

        RateLimiter::hit($key, now()->addHours(12)->diffInSeconds());
    }

    /**
     * Generate unique OTP code
     */
    protected function generateUniqueOtpCode(): string
    {
        return App::environment('production') ? generateUniqueCode(6, ['symbols', 'uppercase', 'lowercase']) : '123456';
    }

    /**
     * Dispatch OTP via the appropriate channel
     */
    protected function dispatchOtp(string $type, string $value, string $code): void
    {
        match ($type) {
            'phone' => $this->sendOtpToPhone($value, $code),
            'email' => $this->sendOtpToEmail($value, $code),
            default => throw new InvalidArgumentException("Unsupported OTP type: {$type}"),
        };
    }

    protected function sendOtpToPhone(string $phone, string $code): void
    {
        // Example: Use SMS provider (Twilio, Vonage, etc.)
        // Twilio::send($phone, "Your OTP code is: {$code}");
        logger("OTP {$code} sent to phone: {$phone}");
    }

    protected function sendOtpToEmail(string $email, string $code): void
    {
        Mail::to($email)->send(new OtpMail($code));
    }

    public function hasOtpTriesLeft(string $type, string $value, ?string $countryCode = null): bool
    {
        return ! RateLimiter::tooManyAttempts("send-otp:long:{$type}:{$value}", 3);
    }
}
