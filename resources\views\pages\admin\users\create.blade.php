<x-layout :title="__('Add User')">
    <x-session-message />
    <div class="card">
        <div class="card-header">{{ __('Add User') }}</div>
        <div class="card-body">
            <form action="{{ route('admin.users.store') }}" method="POST" enctype="multipart/form-data">
                @csrf

                <div class="row">
                    <!-- Name -->
                    <div class="mb-3 col-lg-4">
                        <label for="name" class="form-label"><b>{{ __('Name') }}</b></label>
                        <input type="text" name="name" id="name" class="form-control" value="{{ old('name') }}">
                        <x-input-error name="name" />
                    </div>

                    <!-- Phone -->
                    <div class="mb-3 col-lg-4">
                        <label for="phone" class="form-label"><b>{{ __('Phone') }}</b></label>
                        <input type="number" name="phone" id="phone" class="form-control" value="{{ old('phone') }}">
                        <x-input-error name="phone" />
                    </div>

                    <!-- Email -->
                    <div class="mb-3 col-lg-4">
                        <label for="email" class="form-label"><b>{{ __('Email') }}</b></label>
                        <input type="text" name="email" id="email" class="form-control" value="{{ old('email') }}">
                        <x-input-error name="email" />
                    </div>
                    
                    <!-- Profile Image -->
                    <div class="mb-4 col-lg-4">
                        <label for="formFile" class="form-label"><b>{{ __('Profile Image') }}</b></label>
                        <input name="image" class="form-control" type="file" id="formFile">
                        <x-input-error name="image" />
                    </div>

                    <!-- Location -->
                    <div class="mb-3 col-lg-4 d-none" id="location-wrapper">
                        <label for="location" class="form-label"><b>{{ __('Location') }}</b></label>
                        <div class="input-group" id="location-group">
                            <input type="text" name="location[address]" value="{{ old('location.address') }}" id="location" class="form-control" style="cursor: default" readonly>
                            <button type="button" class="btn btn-outline-secondary" id="openMap">
                                <i class="ti ti-map-2" style="font-size: 20px"></i>
                            </button>
                        </div>
                        <!-- Hidden lat/lng for backend -->
                        <input type="hidden" name="location[lat]" value="{{ old('location.lat') }}" id="location-lat">
                        <input type="hidden" name="location[lng]" value="{{ old('location.lng') }}" id="location-lng">
                        <x-input-error name="location.lat" />
                    </div>
                </div>

                <!-- Bank Fields (essential, hidden until a role is selected) -->
                <div id="bank-fields" class="d-none">
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Bank Name') }}</b></label>
                            <input type="text" name="bank_name" id="bank_name" class="form-control" value="{{ old('bank_name') }}">
                            <x-input-error name="bank_name" />
                        </div>
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Bank Account Number') }}</b></label>
                            <input type="text" name="bank_account_number" id="bank_account_number" class="form-control" value="{{ old('bank_account_number') }}">
                            <x-input-error name="bank_account_number" />
                        </div>
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('IBAN') }}</b></label>
                            <input type="text" name="iban" id="iban" class="form-control" value="{{ old('iban') }}">
                            <x-input-error name="iban" />
                        </div>
                    </div>
                </div>

                <!-- Roles (Inline Checkboxes) -->
                <div class="row">
                    <div class="mb-3 col-lg-6">
                        <label class="form-label d-block"><b>{{ __('Roles') }}</b></label>
                        
                        <div class="form-check form-check-inline me-3">
                            <input class="form-check-input" type="checkbox" name="roles[]" value="supplier"
                                id="role-supplier" @checked(collect(old('roles'))->contains('supplier'))>
                            <label class="form-check-label" for="role-supplier">{{ __('Supplier') }}</label>
                        </div>

                        <div class="form-check form-check-inline me-3">
                            <input class="form-check-input" type="checkbox" name="roles[]" value="technician"
                                id="role-technician" @checked(collect(old('roles'))->contains('technician'))>
                            <label class="form-check-label" for="role-technician">{{ __('Technician') }}</label>
                        </div>
                        
                        <div class="form-check form-check-inline me-3">
                            <input class="form-check-input" type="checkbox" name="roles[]" value="rental_outlet"
                                id="role-rental" @checked(collect(old('roles'))->contains('rental_outlet'))>
                            <label class="form-check-label" for="role-rental">{{ __('Rental Outlet') }}</label>
                        </div>

                        <x-input-error name="roles" />
                    </div>
                </div>

                <!-- Supplier Fields -->
                <div id="supplier-fields" class="d-none">
                    <h5 class="mt-3">{{ __('Supplier Info') }}</h5>
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Type') }}</b></label>
                            <select name="supplier[type]" class="form-select">
                                <option value="wholesale" @selected(old('supplier.type') == 'wholesale')>{{ __('Wholesale') }}</option>
                                <option value="retail" @selected(old('supplier.type') == 'retail')>{{ __('Retail') }}</option>
                            </select>
                            <x-input-error name="supplier.type" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Company Name') }}</b></label>
                            <input type="text" name="supplier[company_name]" class="form-control" value="{{ old('supplier.company_name') }}">
                            <x-input-error name="supplier.company_name" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Commercial Register') }}</b></label>
                            <input type="text" name="supplier[commercial_register]" class="form-control" value="{{ old('supplier.commercial_register') }}">
                            <x-input-error name="supplier.commercial_register" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Tax Number') }}</b></label>
                            <input type="text" name="supplier[tax_number]" class="form-control" value="{{ old('supplier.tax_number') }}">
                            <x-input-error name="supplier.tax_number" />
                        </div>
                    
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Specializations') }}</b></label>
                            <select name="supplier[categories][]" class="form-select select2" multiple>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" @selected(collect(old('supplier.categories'))->contains($category->id))>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            <x-input-error name="supplier.categories" />
                        </div>

                        <!-- Mirrored Phone -->
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Phone') }}</b></label>
                            <input type="text" id="supplier-phone" class="form-control" disabled>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Company Email') }}</b></label>
                            <input type="email" name="supplier[company_email]" class="form-control" value="{{ old('supplier.company_email') }}">
                            <x-input-error name="supplier.company_email" />
                        </div>

                        <!-- Image -->
                        <div class="mb-4 col-lg-4">
                            <label for="formFile" class="form-label"><b>{{ __('Company Logo') }}</b></label>
                            <input name="supplier[company_image]" class="form-control" type="file" id="formFile">
                            <x-input-error name="supplier.company_image" />
                        </div>
                    </div>

                    <div class="row">
                         <!-- Mirrored Bank -->
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Bank Name') }}</b></label>
                            <input type="text" id="supplier-bank-name" class="form-control" disabled>
                        </div>
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Bank Account Number') }}</b></label>
                            <input type="text" id="supplier-bank-account" class="form-control" disabled>
                        </div>
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('IBAN') }}</b></label>
                            <input type="text" id="supplier-iban" class="form-control" disabled>
                        </div>
                    </div>
                </div>

                <!-- Technician Fields -->
                <div id="technician-fields" class="d-none">
                    <h5 class="mt-3">{{ __('Technician Info') }}</h5>
                    <div class="row">
                        <!-- Mirrored Name -->
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Name') }}</b></label>
                            <input type="text" id="technician-name" class="form-control" disabled>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('ID Number') }}</b></label>
                            <input type="text" name="technician[id_number]" class="form-control" value="{{ old('technician.id_number') }}">
                            <x-input-error name="technician.id_number" />
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Nationality') }}</b></label>
                            <select name="technician[nationality_id]" class="form-select select2" id="select_nationality">
                                <option disabled selected>{{ __("Choose") }}...</option>
                                @foreach($countries as $country)
                                    <option value="{{ $country->id }}" @selected(old('technician.nationality_id') == $country->id)>
                                        {{ $country->translated_name }}
                                    </option>
                                @endforeach
                            </select>
                            <x-input-error name="technician.nationality_id" />
                        </div>

                        <!-- Mirrored Phone -->
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Phone') }}</b></label>
                            <input type="text" id="technician-phone" class="form-control" disabled>
                        </div>

                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Specializations') }}</b></label>
                            <select name="technician[specializations][]" class="form-select select2" multiple>
                                @foreach($specializations as $spec)
                                    <option value="{{ $spec->id }}" @selected(collect(old('technician.specializations'))->contains($spec->id))>
                                        {{ $spec->name }}
                                    </option>
                                @endforeach
                            </select>
                            <x-input-error name="technician.specializations" />
                        </div>
                    </div>
                    
                    <!-- Mirrored Bank -->
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Bank Name') }}</b></label>
                            <input type="text" id="technician-bank-name" class="form-control" disabled>
                        </div>
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Bank Account Number') }}</b></label>
                            <input type="text" id="technician-bank-account" class="form-control" disabled>
                        </div>
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('IBAN') }}</b></label>
                            <input type="text" id="technician-iban" class="form-control" disabled>
                        </div>
                    </div>
                </div>

                <!-- Rental Operator Fields -->
                <div id="rental-outlet-fields" class="d-none">
                    <h5 class="mt-3">{{ __('Rental Operator Info') }}</h5>
                    <div class="row">
                        <!-- Mirrored Name -->
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Name') }}</b></label>
                            <input type="text" id="rental-name" class="form-control" disabled>
                        </div>

                        <!-- Mirrored Phone -->
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Phone') }}</b></label>
                            <input type="text" id="rental-phone" class="form-control" disabled>
                        </div>

                        <!-- Mirrored Email -->
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Email') }}</b></label>
                            <input type="text" id="rental-email" class="form-control" disabled>
                        </div>
                    </div>
                    
                    <!-- Mirrored Bank -->
                    <div class="row">
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Bank Name') }}</b></label>
                            <input type="text" id="rental-bank-name" class="form-control" disabled>
                        </div>
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('Bank Account Number') }}</b></label>
                            <input type="text" id="rental-bank-account" class="form-control" disabled>
                        </div>
                        <div class="mb-3 col-lg-4">
                            <label class="form-label"><b>{{ __('IBAN') }}</b></label>
                            <input type="text" id="rental-iban" class="form-control" disabled>
                        </div>
                    </div>
                </div>
                
                <div class="d-flex justify-content-end gap-3 mt-4">
                    <!-- Save Button -->
                    <button type="submit" class="btn btn-primary">
                        {{ __('Save') }}
                    </button>

                    <!-- Back Button -->
                    <a href="{{ route('admin.users.index') }}" class="btn btn-outline-primary">
                        {{ __('Back') }}
                    </a>
                </div>

            </form>
        </div>
    </div>

    <x-location-modal />

    @push('js')
    <script>
        function toggleRoleFields() {
            let roles = [];
            $('input[name="roles[]"]:checked').each(function() {
                roles.push($(this).val());
            });

            $('#supplier-fields').toggleClass('d-none', !roles.includes('supplier'));
            $('#technician-fields').toggleClass('d-none', !roles.includes('technician'));
            $('#rental-outlet-fields').toggleClass('d-none', !roles.includes('rental_outlet'));

            // Bank + Location visible if any role is selected
            let hasRole = roles.length > 0;
            $('#bank-fields').toggleClass('d-none', !hasRole);
            $('#location-wrapper').toggleClass('d-none', !hasRole);
        }


        $('input[name="roles[]"]').on('change', toggleRoleFields);
        toggleRoleFields(); // run on load

        // Mirror essential fields
        function mirrorInputs() {
            // technician
            $('#technician-name').val($('#name').val());
            $('#technician-phone').val($('#phone').val());

            // supplier
            $('#supplier-phone').val($('#phone').val());

            // rental outlet
            $('#rental-name').val($('#name').val());
            $('#rental-phone').val($('#phone').val());
            $('#rental-email').val($('#email').val());

            // mirror bank
            $('#supplier-bank-name').val($('#bank_name').val());
            $('#supplier-bank-account').val($('#bank_account_number').val());
            $('#supplier-iban').val($('#iban').val());

            $('#technician-bank-name').val($('#bank_name').val());
            $('#technician-bank-account').val($('#bank_account_number').val());
            $('#technician-iban').val($('#iban').val());

            $('#rental-bank-name').val($('#bank_name').val());
            $('#rental-bank-account').val($('#bank_account_number').val());
            $('#rental-iban').val($('#iban').val());
        }

        $('#name, #phone, #email, #bank_name, #bank_account_number, #iban').on('input', mirrorInputs);
        mirrorInputs(); // run on load
    </script>

    {{-- location --}}
    @include('partials.location-script', ['lat' => old('location.lat'), 'lng' => old('location.lng')])

    @endpush

</x-layout>