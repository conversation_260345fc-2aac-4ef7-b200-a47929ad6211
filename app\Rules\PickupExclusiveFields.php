<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * Ensures pickup is provided by either address or location, not both.
 */
class PickupExclusiveFields implements ValidationRule
{
    public function __construct() {}

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $hasAddress = request()->filled('pickup_address_id');
        $hasLocation = request()->filled('pickup_location.lat') && request()->filled('pickup_location.lng');

        if ($hasAddress && $hasLocation) {
            $fail(__('validation.pickup_exclusive_fields'));
        }
    }
}
