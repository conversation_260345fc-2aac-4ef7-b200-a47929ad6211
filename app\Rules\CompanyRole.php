<?php

namespace App\Rules;

use App\Repositories\RoleRepository;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class CompanyRole implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $roleRepository = app(RoleRepository::class);

        $role = $roleRepository->getById($value);

        if ($role && $role->type != 'company') {
            $fail(__('not company role'));
        }

        if ($role && $role->name === 'hub admin') {
            $fail(__('hub admin role is not allowed'));
        }
    }
}
