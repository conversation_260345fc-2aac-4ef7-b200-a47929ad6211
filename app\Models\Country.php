<?php

namespace App\Models;

use Altwaireb\World\Models\Country as Model;

class Country extends Model
{
    protected $appends = [
        'translated_name',
        'flag_url',
    ];

    public function getTranslatedNameAttribute(): string
    {
        $locale = app()->getLocale();

        return $this->translations[$locale] ?? $this->name;
    }

    public function getFlagUrlAttribute(): string
    {
        $iso2 = strtolower($this->iso2);

        return "https://flagcdn.com/w40/{$iso2}.png";
    }
}
