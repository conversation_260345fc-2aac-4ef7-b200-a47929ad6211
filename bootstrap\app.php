<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\UnauthorizedHttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        using: function () {
            Route::middleware('web')
                ->group(base_path('routes/web.php'));

            Route::middleware('api', 'setLocale')
                ->prefix('api')
                ->group(base_path('routes/api.php'));

            Route::middleware('web')
                ->prefix('admin')
                ->name('admin.')
                ->group(base_path('routes/admin/routes.php'));

            Route::middleware('web')
                ->prefix('provider')
                ->name('provider.')
                ->group(base_path('routes/provider/routes.php'));

            Route::middleware('api', 'setLocale')
                ->prefix('api/user')
                ->name('user.')
                ->group(base_path('routes/user/routes.php'));
        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->alias([
            'setLocale' => \App\Http\Middleware\SetLocale::class,
            'active.verified' => \App\Http\Middleware\EnsureUserIsActiveAndVerified::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->renderable(function (NotFoundHttpException $e, $request) {
            if (request()->expectsJson()) {
                return error($e->getMessage() ? $e->getMessage() : __('resource not found'), 404);
            }

            return null;
        });
        $exceptions->renderable(function (UnauthorizedHttpException $e, $request) {
            if (request()->expectsJson()) {
                return error($e->getMessage() ? $e->getMessage() : __('unauthorized'), 401);
            }

            return null;
        });
        $exceptions->renderable(function (BadRequestHttpException $e) {
            if (request()->expectsJson()) {
                return error($e->getMessage() ? $e->getMessage() : __('bad request'), 400);
            }

            return back()->with('fail', $e->getMessage())->withInput();
        });
    })->create();
