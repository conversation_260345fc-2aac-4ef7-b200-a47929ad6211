<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class User extends Authenticatable implements HasMedia
{
    use HasApiTokens;
    use HasFactory;
    use InteractsWithMedia;
    use Notifiable;
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'country_code',
        'phone',
        'is_verified',
        'biometric_token',
        'guest_token',
        'location',
        'is_active',
        'username',
        'notification_enabled',
        'biometric_enabled',
        'bank_account_number',
        'bank_name',
        'iban',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_verified' => 'boolean',
            'is_active' => 'boolean',
            'notification_enabled' => 'boolean',
            'biometric_enabled' => 'boolean',
            'password' => 'hashed',
            'biometric_token' => 'string',
            'location' => Point::class,
        ];
    }

    public function updateDeviceToken(array $data): void
    {
        $this->device_token()->updateOrCreate(
            ['device_id' => $data['device_id']],
            [
                'device_id' => $data['device_id'],
                'firebase_token' => $data['firebase_token'],
            ]
        );
    }

    public function socialCredentials(): MorphMany
    {
        return $this->morphMany(SocialCredential::class, 'credentialable');
    }

    public function otps(): MorphMany
    {
        return $this->morphMany(Otp::class, 'otpeable');
    }

    public function device_token(): MorphOne
    {
        return $this->morphOne(DeviceToken::class, 'userable');
    }

    public function supplier()
    {
        return $this->hasOne(Supplier::class)->where('approval_status', 'approved');
    }

    public function rentalOutlet()
    {
        return $this->hasOne(RentalOutlet::class)->where('approval_status', 'approved');
    }

    public function resetCodes(): MorphMany
    {
        return $this->morphMany(PasswordResetCode::class, 'user');
    }

    public function pendingRentalOutlet()
    {
        return $this->hasOne(RentalOutlet::class)->where('approval_status', 'pending');
    }

    public function pendingTechnician()
    {
        return $this->hasOne(Technician::class)->where('approval_status', 'pending');
    }

    public function technician()
    {
        return $this->hasOne(Technician::class)->where('approval_status', 'approved');
    }
}
