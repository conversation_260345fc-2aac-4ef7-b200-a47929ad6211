<?php

namespace App\Providers;

use Illuminate\Auth\Middleware\Authenticate;
use Illuminate\Auth\Middleware\RedirectIfAuthenticated;
use Illuminate\Support\ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        Authenticate::redirectUsing(function () {
            if (request()->routeIs('admin.*')) {
                return route('admin.login');
            }

            return route('provider.login');
        });

        RedirectIfAuthenticated::redirectUsing(function () {
            if (request()->routeIs('admin.*')) {
                return route('admin.index');
            }

            return route('provider.index');
        });
    }
}
