<?php

namespace App\Services\Admin;

use App\Repositories\AdminRepository;
use App\Services\MediaUploaderService;
use Illuminate\Support\Facades\DB;

class ProfileService
{
    public function __construct(
        private readonly AdminRepository $adminRepository,
        private readonly MediaUploaderService $mediaService,
    ) {}

    public function update(array $data)
    {
        $admin = auth('admin')->user();

        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['password'] = isset($data['password']) ? bcrypt($data['password']) : $admin->password;

        DB::transaction(function () use ($admin, $data) {
            $this->adminRepository->update($admin, $data);

            if (isset($data['image'])) {
                $admin->clearMediaCollection();
                $this->mediaService->upload($admin, $data['image']);
            }
        });
    }
}
