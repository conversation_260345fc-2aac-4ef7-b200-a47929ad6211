<?php

namespace App\Services\Admin;

use App\Repositories\AdminRepository;
use Illuminate\Support\Facades\DB;

class ProfileService
{
    public function __construct(
        private readonly AdminRepository $adminRepository,
        private readonly MediaUploaderService $mediaService,
    ) {}

    public function updateProfile()
    {
        $admin = auth('admin')->user();
        $data = request()->all();

        $data['phone'] = normalizePhoneNumber($data['phone']);
        $data['password'] = request('password') ? bcrypt(request('password')) : $admin->password;

        DB::transaction(function () use ($admin, $data) {
            $this->adminRepository->update($admin, $data);

            if (request('image')) {
                if ($admin->image) {
                    $this->mediaService->delete($admin->image);
                }
                $this->mediaService->upload($admin, request('image'), 'admins', 'profile');
            }
        });
    }
}
