<?php

namespace App\Models;

use App\Enums\ApprovalStatus;
use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Supplier extends Model implements HasMedia
{
    use InteractsWithMedia, HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'company_name',
        'company_email',
        'tax_number',
        'commercial_register',
        'approval_status',
    ];

    public function casts(): array
    {
        return [
            'approval_status' => ApprovalStatus::class,
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'supplier_category');
    }

    public function getCompanyLogoAttribute()
    {
        return $this->getMedia('*')->first();
    }
}
