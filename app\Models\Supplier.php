<?php

namespace App\Models;

use App\Enums\ApprovalStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Supplier extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, SoftDeletes;

    protected $fillable = [
        'user_id',
        'type',
        'company_name',
        'company_email',
        'commercial_register',
        'tax_number',
        'approval_status',
    ];

    public function casts(): array
    {
        return [
            'approval_status' => ApprovalStatus::class,
        ];
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'supplier_category');
    }

    public function getCompanyEmailAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getComercialRegisterAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getTaxNumberAttribute($value)
    {
        return $this->deleted_at ? restoreInvalidatedValue($value) : $value;
    }

    public function getCompanyLogoAttribute()
    {
        return $this->getMedia('*')->first();
    }

    public function getCompanyLogoUrlAttribute()
    {
        return $this->company_logo ? $this->company_logo->getUrl() : asset('placeholder-img.png');
    }
}
