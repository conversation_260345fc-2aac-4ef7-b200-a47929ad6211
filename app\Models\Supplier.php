<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;

class Supplier extends Model
{
    use InteractsWithMedia;

    protected $fillable = [
        'user_id',
        'type',
        'company_name',
        'company_email',
        'tax_number',
        'commercial_register',
        'approval_status',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'supplier_category');
    }

    public function getCompanyLogoAttribute()
    {
        return $this->getMedia('*')->first();
    }
}
