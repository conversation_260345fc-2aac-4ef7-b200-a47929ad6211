<?php

namespace App\Http\Controllers\Provider\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ConfirmOtpRequest;
use App\Http\Requests\Admin\LoginRequest;
use App\Repositories\UserRepository;
use App\Traits\HasOtp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class AuthenticationController extends Controller
{
    use HasOtp;

    const OTP_RESEND_BLOCK_HOURS = 60 * 60 * 12;

    public function __construct(private readonly UserRepository $userRepository) {}

    public function loginView()
    {
        return view('auth.login');
    }

    public function login(LoginRequest $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;
        $channel = $userByEmailOrUsername ? 'email' : 'phone';

        if (!$user || (!$user->supplier && !$user->rentalOutlet)) {
            throw ValidationException::withMessages([
                'username' => __('auth.failed'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("login-attempts-provider:$user->id", 3)) {

            $seconds = RateLimiter::availableIn("login-attempts-provider:$user->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' =>  __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("login-attempts-user:$user->id", 120);

        if (!Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'username' => __('auth.failed'),
            ]);
        }

        if (!$user->is_active) {
            throw ValidationException::withMessages([
                'username' => __('your account is disabled'),
            ]);
        }

        // send code if hasn't reached limit
        if (!RateLimiter::tooManyAttempts("otp-send-attempts-provider:$user->id", 3)) {
            $code = $this->generateUniqueOtpCode();

            $user->otps()->create([
                'code' => $code,
                'expires_at' => now()->addMinutes(5)
            ]);

            if ($userByEmailOrUsername) {
                $this->dispatchOtp('email', $user->email, $code);
            }

            if ($userByPhone) {
                $this->dispatchOtp('phone', $user->phone, $code);
            }
        }

        RateLimiter::hit("otp-send-attempts-user:$user->id", self::OTP_RESEND_BLOCK_HOURS);

        $channel = $userByEmailOrUsername ? 'email' : 'phone';

        session()->flash('username', $request->username);
        session()->flash('password', $request->password);
        session()->flash('channel', $channel);

        return to_route('provider.confirm-otp');
    }

    public function confirmOtpView()
    {
        if (!session()->has('username') || !session()->has('password')) {
            return to_route('provider.login');
        }

        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername(session('username'));
        $userByPhone = $this->userRepository->getByPhone(session('username'));
        $user = $userByEmailOrUsername ?? $userByPhone;

        $can_resend = !RateLimiter::tooManyAttempts("otp-send-attempts-user:$user->id", 3);

        $lastOtp = $user->otps()->latest()->first();

        $available_in = null;

        if ($lastOtp) {
            $secondsDiff = abs(now()->diffInSeconds($lastOtp->created_at));

            // If between 1 and 60 seconds, set remaining wait time
            if ($secondsDiff >= 0 && $secondsDiff <= 60) {
                $available_in = (int) (60 - $secondsDiff);
            }
        }

        return view('auth.confirm-otp', compact('can_resend', 'available_in'));
    }

    public function confirmOtp(ConfirmOtpRequest $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;

        if (!$user || (!$user->supplier && !$user->rentalOutlet)) {
            throw ValidationException::withMessages([
                'username' => __('user not found.'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("otp-confirm-attempts-user:$user->id", 3)) {
            $seconds = RateLimiter::availableIn("otp-confirm-attempts-user:$user->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' =>  __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("otp-confirm-attempts-user:$user->id", 120);

        if (!Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages([
                'username' => __('auth.failed'),
            ]);
        }

        if (!$user->is_active) {
            throw ValidationException::withMessages([
                'username' => __('your account is disabled'),
            ]);
        }

        $otp = $user->otps()->where('code', $request->otp)->latest()->first();

        if (!$otp) {
            throw ValidationException::withMessages([
                'otp' => __('invalid otp'),
            ]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages([
                'otp' => __('expired otp'),
            ]);
        }

        auth('provider')->login($user);

        $user->otps()->delete();

        return success(true);
    }

    public function resendOtp(Request $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;

        if (!$user || (!$user->supplier && !$user->rentalOutlet)) {
            throw ValidationException::withMessages([
                'username' => __('user not found.'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("otp-send-attempts-user:$user->id", 3)) {

            $seconds = RateLimiter::availableIn("otp-send-attempts-user:$user->id");
            $remainingTime = formatSecondsToHoursTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('limit reached: retry after :time hours', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("otp-send-attempts-user:$user->id", self::OTP_RESEND_BLOCK_HOURS);

        $code = $this->generateUniqueOtpCode();

        $user->otps()->create([
            'code' => $code,
            'expires_at' => now()->addHours(6)
        ]);

        if ($userByEmailOrUsername) {
            $this->dispatchOtp('email', $user->email, $code);
        }

        if ($userByPhone) {
            $this->dispatchOtp('phone', $user->phone, $code);
        }

        return success([
            'can_resend' => !RateLimiter::tooManyAttempts("otp-send-attempts-user:$user->id", 3)
        ]);
    }

    public function logout(Request $request)
    {
        auth('provider')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return to_route('provider.login');
    }
}
