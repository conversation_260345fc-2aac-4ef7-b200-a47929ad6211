<?php

namespace App\Http\Controllers\Provider\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ConfirmOtpRequest;
use App\Http\Requests\Admin\LoginRequest;
use App\Repositories\UserRepository;
use App\Traits\HasOtp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class AuthenticationController extends Controller
{
    use HasOtp;

    /** Login Attempts */
    private const LOGIN_ATTEMPTS_LIMIT = 3;

    private const LOGIN_LOCK_SECONDS = 120;

    /** OTP Sending Limits */
    private const OTP_SEND_ATTEMPTS_LIMIT = 3;

    private const OTP_EXPIRY_MINUTES = 5;

    private const OTP_RESEND_BLOCK_SECONDS = 60 * 60 * 12; // 12 hours

    /** OTP Confirmation Limits */
    private const OTP_CONFIRM_ATTEMPTS_LIMIT = 3;

    private const OTP_CONFIRM_LOCK_SECONDS = 120;

    /** Resend Cooldown */
    private const OTP_RESEND_WAIT_SECONDS = 60; // 1 minute cooldown

    public function __construct(private readonly UserRepository $userRepository) {}

    /** Show login form */
    public function loginView()
    {
        return view('auth.login');
    }

    /** Handle login and send OTP */
    public function login(LoginRequest $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;

        if (! $user || (! $user->supplier && ! $user->rentalOutlet)) {
            throw ValidationException::withMessages([
                'username' => __('auth.failed'),
            ]);
        }

        // Rate limit login attempts
        if (RateLimiter::tooManyAttempts("login-attempts-provider:$user->id", self::LOGIN_ATTEMPTS_LIMIT)) {
            $seconds = RateLimiter::availableIn("login-attempts-provider:$user->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("login-attempts-provider:$user->id", self::LOGIN_LOCK_SECONDS);

        if (! Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages(['username' => __('auth.failed')]);
        }

        if (! $user->is_active) {
            throw ValidationException::withMessages(['username' => __('your account is disabled')]);
        }

        // Send OTP if under limit and wait seconds
        if (
            ! RateLimiter::tooManyAttempts("otp-send-attempts-provider:$user->id", self::OTP_SEND_ATTEMPTS_LIMIT)
            && ! RateLimiter::tooManyAttempts("otp-resend-wait-provider:$user->id", 1)
        ) {
            $code = $this->generateUniqueOtpCode();

            $user->otps()->create([
                'code' => $code,
                'expires_at' => now()->addMinutes(self::OTP_EXPIRY_MINUTES),
            ]);

            if ($userByEmailOrUsername) {
                $this->dispatchOtp('email', $user->email, $code);
            }

            if ($userByPhone) {
                $this->dispatchOtp('phone', $user->phone, $code);
            }

            // Start resend cooldown
            RateLimiter::hit("otp-resend-wait-provider:$user->id", self::OTP_RESEND_WAIT_SECONDS);

            // Count OTP send attempt
            RateLimiter::hit("otp-send-attempts-provider:$user->id", self::OTP_RESEND_BLOCK_SECONDS);
        }

        $channel = $userByEmailOrUsername ? 'email' : 'phone';

        session()->flash('username', $request->username);
        session()->flash('password', $request->password);
        session()->flash('channel', $channel);

        return to_route('provider.confirm-otp');
    }

    /** Show OTP confirmation screen */
    public function confirmOtpView()
    {
        if (! session()->has('username') || ! session()->has('password')) {
            return to_route('provider.login');
        }

        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername(session('username'));
        $userByPhone = $this->userRepository->getByPhone(session('username'));
        $user = $userByEmailOrUsername ?? $userByPhone;

        // Can resend if not exceeded send attempts limit
        $can_resend = ! RateLimiter::tooManyAttempts("otp-send-attempts-provider:$user->id", self::OTP_SEND_ATTEMPTS_LIMIT);
        $wait_seconds = RateLimiter::availableIn("otp-resend-wait-provider:$user->id");

        return view('auth.confirm-otp', compact('can_resend', 'wait_seconds'));
    }

    /** Handle OTP confirmation */
    public function confirmOtp(ConfirmOtpRequest $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;

        if (! $user) {
            throw ValidationException::withMessages(['username' => __('auth.failed')]);
        }

        // Rate limit OTP confirmation attempts
        if (RateLimiter::tooManyAttempts("otp-confirm-attempts-provider:$user->id", self::OTP_CONFIRM_ATTEMPTS_LIMIT)) {
            $seconds = RateLimiter::availableIn("otp-confirm-attempts-provider:$user->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("otp-confirm-attempts-provider:$user->id", self::OTP_CONFIRM_LOCK_SECONDS);

        if (! Hash::check($request->password, $user->password)) {
            throw ValidationException::withMessages(['username' => __('auth.failed')]);
        }

        if (! $user->is_active) {
            throw ValidationException::withMessages(['username' => __('your account is disabled')]);
        }

        $otp = $user->otps()->where('code', $request->otp)->latest()->first();

        if (! $otp) {
            throw ValidationException::withMessages(['otp' => __('invalid otp')]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages(['otp' => __('expired otp')]);
        }

        auth('provider')->login($user);
        $user->otps()->delete();

        return success(true);
    }

    /** Resend OTP */
    public function resendOtp(Request $request)
    {
        $userByEmailOrUsername = $this->userRepository->findByEmailOrUsername($request->username);
        $userByPhone = $this->userRepository->getByPhone($request->username);
        $user = $userByEmailOrUsername ?? $userByPhone;

        if (! $user) {
            throw ValidationException::withMessages(['username' => __('user not found.')]);
        }

        // Check resend cooldown (to prevent spam)
        if (RateLimiter::tooManyAttempts("otp-resend-wait-provider:$user->id", 1)) {
            $wait_seconds = RateLimiter::availableIn("otp-resend-wait-provider:$user->id");
            throw ValidationException::withMessages([
                'username' => __('please wait :seconds seconds before resending OTP', ['seconds' => $wait_seconds]),
            ]);
        }

        // Check overall send attempts limit (global daily/hourly cap)
        if (RateLimiter::tooManyAttempts("otp-send-attempts-provider:$user->id", self::OTP_SEND_ATTEMPTS_LIMIT)) {
            $seconds = RateLimiter::availableIn("otp-send-attempts-provider:$user->id");
            $remainingTime = formatSecondsToHoursTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('limit reached: retry after :time hours', ['time' => $remainingTime]),
            ]);
        }

        // Generate & send new OTP
        $code = $this->generateUniqueOtpCode();

        $user->otps()->create([
            'code' => $code,
            'expires_at' => now()->addMinutes(self::OTP_EXPIRY_MINUTES),
        ]);

        if ($userByEmailOrUsername) {
            $this->dispatchOtp('email', $user->email, $code);
        }

        if ($userByPhone) {
            $this->dispatchOtp('phone', $user->phone, $code);
        }

        // Start resend cooldown
        RateLimiter::hit("otp-resend-wait-provider:$user->id", self::OTP_RESEND_WAIT_SECONDS);

        // Increment global send attempts
        RateLimiter::hit("otp-send-attempts-provider:$user->id", self::OTP_RESEND_BLOCK_SECONDS);

        return success([
            'can_resend' => ! RateLimiter::tooManyAttempts("otp-send-attempts-provider:$user->id", self::OTP_SEND_ATTEMPTS_LIMIT),
        ]);
    }

    /** Logout Provider */
    public function logout(Request $request)
    {
        auth('provider')->logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return to_route('provider.login');
    }
}
