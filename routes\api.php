<?php

use App\Http\Controllers\Api\General\DdlController;
use Illuminate\Support\Facades\Route;

Route::prefix('ddl')->group(function () {
    Route::get('countries', [DdlController::class, 'getCountries']);
    Route::get('countries/{country}/states', [DdlController::class, 'getStates']);
    Route::get('countries/{country}/cities', [DdlController::class, 'getCitiesOfCountry']);
    Route::get('states/{state}/cities', [DdlController::class, 'getCities']);
    Route::get('specializations', [DdlController::class, 'getSpecializations']);
    Route::get('categories', [DdlController::class, 'getCategories']);
});
