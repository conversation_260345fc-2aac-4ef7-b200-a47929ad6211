<?php

use App\Http\Controllers\Api\User\ProfileController;
use App\Http\Controllers\Api\User\RentalOperatorController;
use App\Http\Controllers\Api\User\SupplierController;
use App\Http\Controllers\Api\User\TechnicianController;
use Illuminate\Support\Facades\Route;

include base_path('routes/user/auth.php');

Route::middleware(['auth:sanctum', 'active.verified'])->group(function () {
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'index'])->name('index');
        Route::put('/', [ProfileController::class, 'update'])->name('update');
        Route::prefix('toggle')->group(function () {
            Route::get('/biometric', [ProfileController::class, 'toggleBiometric'])->name('toggle.biometric');
            Route::get('/notifications', [ProfileController::class, 'toggleNotifications'])->name('toggle.notifications');
        });
        Route::post('/upgrade-to-supplier', [SupplierController::class, 'store']);
        Route::post('/upgrade-to-rental-operator', [RentalOperatorController::class, 'store']);
        Route::post('/upgrade-to-technician', [TechnicianController::class, 'store']);
        Route::delete('/delete-account', [ProfileController::class, 'deleteAccount'])->name('delete');
    });
});
