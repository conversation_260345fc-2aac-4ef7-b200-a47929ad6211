<?php

namespace App\Repositories;

use App\Models\GroupNotification;

class GroupNotificationRepository
{
    public function __construct(private readonly GroupNotification $model)
    {
        //
    }

    public function all()
    {
        return $this->model->all();
    }

    public function getPaginated(array $filters = [], int $limit = 10)
    {
        return $this->model
            ->orderByDesc('id')
            ->paginate($filters['per_page'] ?? $limit);
    }

    public function create(array $data): GroupNotification
    {
        return $this->model->create($data);
    }

    public function find(int $id): ?GroupNotification
    {
        return $this->model->find($id);
    }

    public function findBySender(object $sender): ?GroupNotification
    {
        return $this->model->where('senderable_id', $sender->id)
            ->where('senderable_type', $sender::class)
            ->first();
    }
}
