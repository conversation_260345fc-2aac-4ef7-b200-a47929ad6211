<?php

namespace App\Services\Admin;

use App\Repositories\SettingRepository;

class SettingService
{
    public function __construct(private readonly SettingRepository $settingRepository)
    {
        //
    }

    public function getAllAdminSettings()
    {
        return $this->settingRepository->getAllAdminSettings();
    }

    public function updateAdminSettings(array $data): void
    {
        foreach ($data as $key => $translations) {
            $setting = $this->settingRepository->getAdminSetting($key);

            if (! $setting) {
                continue;
            }

            // Determine if this is a translatable setting
            $isTranslatable = in_array($setting->type, ['text', 'long_text']) && $setting->group === 'general';

            // Initialize the value array with all supported languages
            $value = [];
            $languages = ['en', 'ar', 'ur'];

            if ($isTranslatable) {
                // Handle translatable settings
                foreach ($languages as $lang) {
                    // Always include the submitted value, even if empty
                    $value[$lang] = $translations[$lang] ?? '';
                }
            } else {
                // Handle non-translatable settings
                $newValue = $translations['en'] ?? '';

                // For boolean settings, convert to proper boolean value
                if ($setting->type === 'boolean') {
                    $newValue = filter_var($newValue, FILTER_VALIDATE_BOOLEAN);
                }

                // For numeric settings, ensure empty string becomes 0 or null
                if ($setting->type === 'number' && $newValue === '') {
                    $newValue = null;
                }

                // Use the same value for all languages in non-translatable settings
                $value = array_fill_keys($languages, $newValue);
            }

            $setting->update(['value' => $value]);
        }
    }
}
