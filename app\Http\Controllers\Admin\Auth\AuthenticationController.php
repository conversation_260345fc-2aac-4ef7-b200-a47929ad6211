<?php

namespace App\Http\Controllers\Admin\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\ConfirmOtpRequest;
use App\Http\Requests\Admin\LoginRequest;
use App\Repositories\AdminRepository;
use App\Traits\HasOtp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Validation\ValidationException;

class AuthenticationController extends Controller
{
    use HasOtp;

    const OTP_RESEND_BLOCK_HOURS = 60 * 60 * 12;

    public function __construct(private readonly AdminRepository $adminRepository) {}

    public function loginView()
    {
        return view('auth.login');
    }

    public function login(LoginRequest $request)
    {
        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername($request->username);
        $adminByPhone = $this->adminRepository->getByPhone($request->username);
        $admin = $adminByEmailOrUsername ?? $adminByPhone;
        $channel = $adminByEmailOrUsername ? 'email' : 'phone';

        if (!$admin) {
            throw ValidationException::withMessages([
                'username' => __('auth.failed'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("login-attempts-admin:$admin->id", 3)) {

            $seconds = RateLimiter::availableIn("login-attempts-admin:$admin->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' =>  __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("login-attempts-admin:$admin->id", 120);

        if (!Hash::check($request->password, $admin->password)) {
            throw ValidationException::withMessages([
                'username' => __('auth.failed'),
            ]);
        }

        if (!$admin->is_active) {
            throw ValidationException::withMessages([
                'username' => __('your account is disabled'),
            ]);
        }

        // send code if hasn't reached limit
        if (!RateLimiter::tooManyAttempts("otp-send-attempts-admin:$admin->id", 3)) {
            $code = $this->generateUniqueOtpCode();

            $admin->otps()->create([
                'code' => $code,
                'expires_at' => now()->addMinutes(5)
            ]);

            if ($adminByEmailOrUsername) {
                $this->dispatchOtp('email', $admin->email, $code);
            }

            if ($adminByPhone) {
                $this->dispatchOtp('phone', $admin->phone, $code);
            }
        }

        RateLimiter::hit("otp-send-attempts-admin:$admin->id", self::OTP_RESEND_BLOCK_HOURS);

        $channel = $adminByEmailOrUsername ? 'email' : 'phone';

        session()->flash('username', $request->username);
        session()->flash('password', $request->password);
        session()->flash('channel', $channel);

        return to_route('admin.confirm-otp');
    }

    public function confirmOtpView()
    {
        if (!session()->has('username') || !session()->has('password')) {
            return to_route('admin.login');
        }

        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername(session('username'));
        $adminByPhone = $this->adminRepository->getByPhone(session('username'));
        $admin = $adminByEmailOrUsername ?? $adminByPhone;

        $can_resend = !RateLimiter::tooManyAttempts("otp-send-attempts-admin:$admin->id", 3);

        $lastOtp = $admin->otps()->latest()->first();

        $available_in = null;

        if ($lastOtp) {
            $secondsDiff = abs(now()->diffInSeconds($lastOtp->created_at));

            // If between 1 and 60 seconds, set remaining wait time
            if ($secondsDiff >= 0 && $secondsDiff <= 60) {
                $available_in = (int) (60 - $secondsDiff);
            }
        }

        return view('auth.confirm-otp', compact('can_resend', 'available_in'));
    }

    public function confirmOtp(ConfirmOtpRequest $request)
    {
        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername($request->username);
        $adminByPhone = $this->adminRepository->getByPhone($request->username);
        $admin = $adminByEmailOrUsername ?? $adminByPhone;

        if (!$admin) {
            throw ValidationException::withMessages([
                'username' => __('auth.failed'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("otp-confirm-attempts-admin:$admin->id", 3)) {
            $seconds = RateLimiter::availableIn("otp-confirm-attempts-admin:$admin->id");
            $remainingTime = formatSecondsToMinutesTime($seconds);

            throw ValidationException::withMessages([
                'username' =>  __('too many attempts: retry after :time minutes', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("otp-confirm-attempts-admin:$admin->id", 120);

        if (!Hash::check($request->password, $admin->password)) {
            throw ValidationException::withMessages([
                'username' => __('auth.failed'),
            ]);
        }

        if (!$admin->is_active) {
            throw ValidationException::withMessages([
                'username' => __('your account is disabled'),
            ]);
        }

        $otp = $admin->otps()->where('code', $request->otp)->latest()->first();

        if (!$otp) {
            throw ValidationException::withMessages([
                'otp' => __('invalid otp'),
            ]);
        }

        if ($otp->expires_at < now()) {
            throw ValidationException::withMessages([
                'otp' => __('expired otp'),
            ]);
        }

        auth('admin')->login($admin);

        $admin->otps()->delete();

        return success(true);
    }

    public function resendOtp(Request $request)
    {
        $adminByEmailOrUsername = $this->adminRepository->getByEmailOrUsername($request->username);
        $adminByPhone = $this->adminRepository->getByPhone($request->username);
        $admin = $adminByEmailOrUsername ?? $adminByPhone;

        if (!$admin) {
            throw ValidationException::withMessages([
                'username' => __('user not found.'),
            ]);
        }

        if (RateLimiter::tooManyAttempts("otp-send-attempts-admin:$admin->id", 3)) {

            $seconds = RateLimiter::availableIn("otp-send-attempts-admin:$admin->id");
            $remainingTime = formatSecondsToHoursTime($seconds);

            throw ValidationException::withMessages([
                'username' => __('limit reached: retry after :time hours', ['time' => $remainingTime]),
            ]);
        }

        RateLimiter::hit("otp-send-attempts-admin:$admin->id", self::OTP_RESEND_BLOCK_HOURS);

        $code = $this->generateUniqueOtpCode();

        $admin->otps()->create([
            'code' => $code,
            'expires_at' => now()->addHours(6)
        ]);

        if ($adminByEmailOrUsername) {
            $this->dispatchOtp('email', $admin->email, $code);
        }

        if ($adminByPhone) {
            $this->dispatchOtp('phone', $admin->phone, $code);
        }

        return success([
            'can_resend' => !RateLimiter::tooManyAttempts("otp-send-attempts-admin:$admin->id", 3)
        ]);
    }

    public function logout(Request $request)
    {
        auth('admin')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return to_route('admin.login');
    }
}
