<?php

namespace App\Http\Controllers\Api\User;

use App\DTO\User\BiometricLoginData;
use App\DTO\User\GuestLoginData;
use App\DTO\User\LoginData;
use App\DTO\User\OtpLoginData;
use App\DTO\User\OtpVerificationData;
use App\DTO\User\RegisterData;
use App\DTO\User\SocialLoginData;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\User\Auth\GuestLoginRequest;
use App\Http\Requests\Api\User\Auth\LoginRequest;
use App\Http\Requests\Api\User\Auth\LoginWithBiometricRequest;
use App\Http\Requests\Api\User\Auth\NewPasswordRequest;
use App\Http\Requests\Api\User\Auth\RegisterBiometricRequest;
use App\Http\Requests\Api\User\Auth\RegisterRequest;
use App\Http\Requests\Api\User\Auth\SendOtpRequest;
use App\Http\Requests\Api\User\Auth\SocialLoginRequest;
use App\Http\Requests\Api\User\Auth\VerifyOtpRequest;
use App\Services\User\AuthService;
use App\Services\User\BiometricAuthService;
use App\Services\User\GuestAuthService;
use App\Services\User\OtpAuthService;
use App\Services\User\PasswordAuthService;
use App\Services\User\SocialAuthService;

class AuthController extends Controller
{
    public function __construct(
        private readonly AuthService $authService,
        private readonly PasswordAuthService $passwordAuthService,
        private readonly OtpAuthService $otpAuthService,
        private readonly SocialAuthService $socialAuthService,
        private readonly GuestAuthService $guestAuthService,
        private readonly BiometricAuthService $biometricAuthService
    ) {
        //
    }

    public function register(RegisterRequest $request)
    {
        $dto = RegisterData::from($request->validated());

        return success($this->authService->register($dto));
    }

    public function login(LoginRequest $request)
    {
        $dto = LoginData::from($request->validated());

        return success($this->passwordAuthService->login($dto));
    }

    public function registerBiometric(RegisterBiometricRequest $request)
    {
        return success($this->biometricAuthService->register($request->validated('biometric_token')));
    }

    public function loginWithBiometric(LoginWithBiometricRequest $request)
    {
        $dto = BiometricLoginData::from($request->validated());

        return success($this->biometricAuthService->login($dto));
    }

    public function loginWithSocial(SocialLoginRequest $request)
    {
        $dto = SocialLoginData::from($request->validated());

        return success($this->socialAuthService->login($dto));
    }

    public function loginAsGuest(GuestLoginRequest $request)
    {
        $dto = GuestLoginData::from($request->validated());

        return success($this->guestAuthService->login($dto));
    }

    public function sendOtp(SendOtpRequest $request)
    {
        $dto = OtpLoginData::from($request->validated());

        return success($this->otpAuthService->sendOtp($dto));
    }

    public function verifyOtp(VerifyOtpRequest $request)
    {
        $dto = OtpVerificationData::from($request->validated());

        return success($this->otpAuthService->verifyOtp($dto));
    }

    public function resetPassword(NewPasswordRequest $request)
    {
        return success($this->authService->resetPassword($request->password));
    }

    public function logout()
    {
        return success($this->authService->logout());
    }

    public function canSendOtp(SendOtpRequest $request)
    {
        return success($this->otpAuthService->canSendOtp($request->validated()));
    }
}
