<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('bank_name')->nullable()->after('location');
            $table->string('bank_account_number')->nullable()->after('bank_name');
            $table->string('iban')->nullable()->after('bank_account_number');
        });

        Schema::table('technicians', function (Blueprint $table) {
            $table->dropColumn(['bank_account_number', 'iban']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn(['bank_name', 'bank_account_number', 'iban']);
        });

        Schema::table('technicians', function (Blueprint $table) {
            $table->string('bank_account_number');
            $table->string('iban')->nullable();
        });
    }
};
