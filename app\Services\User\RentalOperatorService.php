<?php

namespace App\Services\User;

use App\DTO\RentalOperator\StoreRentalOperatorData;
use App\Enums\ApprovalStatus;
use App\Models\RentalOutlet;
use App\Repositories\RentalOperatorRepository;
use App\Repositories\UserRepository;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpKernel\Exception\BadRequestHttpException;

class RentalOperatorService
{
    public function __construct(
        private readonly RentalOperatorRepository $rentalOperatorRepository,
        private readonly UserRepository $userRepository
    ) {
        //
    }

    /**
     * Create a new rental outlet.
     */
    public function create(StoreRentalOperatorData $dto): RentalOutlet
    {
        $user = auth('user')->user();

        if ($user->rentalOutlet()->exists()) {
            throw new BadRequestHttpException(__('You already have an approved rental operator upgrade request.'));
        }

        if ($user->pendingRentalOutlet()->exists()) {
            throw new BadRequestHttpException(__('You already have a pending rental operator upgrade request.'));
        }

        return DB::transaction(function () use ($user, $dto) {
            if (! empty($dto->userData())) {
                $this->userRepository->update($user, $dto->userData());
            }

            return $this->rentalOperatorRepository->create(
                array_merge($dto->rentalOperatorData(), [
                    'user_id' => $user->id,
                    'approval_status' => ApprovalStatus::PENDING,
                ])
            );
        });
    }
}
