<?php

namespace App\View\Components;

use Illuminate\View\Component;

class CountCard extends Component
{
    public string $icon;

    public string $color;

    public string $label;

    public int|string $count;

    public function __construct(string $icon, string $color, string $label, int|string $count = 0)
    {
        $this->icon = $icon;
        $this->color = $color;
        $this->label = $label;
        $this->count = $count;
    }

    public function render()
    {
        return view('components.count-card');
    }
}
