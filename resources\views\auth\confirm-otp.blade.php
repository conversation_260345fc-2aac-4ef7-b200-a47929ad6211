@php
$guard = request()->routeIs('admin.*') ? 'admin' : 'provider';
$confirmation_link = request()->routeIs('*.confirm-reset-code') ? route("$guard.confirm-reset-code") : route("$guard.confirm-otp");
$resend_link = request()->routeIs('*.confirm-reset-code') ? route("$guard.resend-reset-code") : route("$guard.resend-otp");
$success_link = request()->routeIs('*.confirm-reset-code') ? route("$guard.reset-password") : route("$guard.index");
@endphp

<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" @if(app()->getLocale() == 'ar') dir="rtl" @else dir="ltr" @endif>

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>{{ __('Confirm OTP') }}</title>
	<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
	<style>
		body {
			background-color: #3a3a3a;
			font-family: 'Noto Kufi Arabic', Tahoma, sans-serif;
		}

		.auth-card {
			border-radius: 1rem;
			overflow: hidden;
			background-color: #fff;
			box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
		}

		.auth-left {
			padding: 3rem 2rem;
			display: flex;
			flex-direction: column;
			justify-content: center;
		}

		.auth-left h4 {
			font-size: 1.25rem;
			font-weight: bold;
			margin-bottom: 1.5rem;
		}

		/* Right section background */
		.auth-right {
			background: url('{{ url("benaa_background.jpg") }}') center center / cover no-repeat;
			position: relative;
		}

		.auth-right::before {
			content: "";
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: rgba(0, 0, 0, 0.6);
		}

		.auth-right-content {
			position: relative;
			z-index: 2;
			text-align: center;
			color: #fff;
			padding: 3rem 2rem;
			display: flex;
			flex-direction: column;
			justify-content: center;
			height: 100%;
		}

		.auth-right-content img {
			max-height: 100px;
			width: 150px;
			margin: 0 auto;
			margin-bottom: 2rem;
		}

		/* OTP inputs */
		.otp-inputs .form-control {
			width: 60px;
			height: 70px;
			font-size: 1.5rem;
			text-align: center;
			border-radius: 0.75rem;
			border: 1px solid #ccc;
			padding: 0;
		}

		.otp-inputs .form-control:focus {
			border-color: #1f2937;
			box-shadow: 0 0 0 0.2rem rgba(31, 41, 55, 0.2);
			outline: none;
		}

		.otp-inputs {
			display: flex;
			justify-content: center;
			gap: 0.75rem;
			margin: 2rem 0;
		}

		.resend-text {
			text-align: center;
			margin-bottom: 1rem;
			font-size: 0.95rem;
		}

		.resend-text a {
			color: #d97706;
			font-weight: 600;
			text-decoration: none;
		}

		.resend-text a:hover {
			text-decoration: underline;
		}

		/* Button styling */
		.btn-submit {
			background-color: #1f2937;
			color: #fff;
			font-size: 1rem;
			font-weight: 500;
			height: 48px;
			border-radius: 0.5rem;
			border: none;
			transition: background-color 0.3s ease;
		}

		.btn-submit:hover {
			background-color: #374151;
		}

		/* Disabled button but keep bg */
		.btn-submit:disabled {
			background-color: #555 !important;
			color: #fff !important;
			opacity: 1 !important;
			cursor: not-allowed;
		}

		/* small error */
		#otp-error {
			display: none;
			color: #d32f2f;
			font-size: 0.9rem;
			text-align: center;
			margin-top: .5rem;
		}

		/* small success */
		#otp-success {
			display: none;
			color:rgb(33, 160, 33);
			font-size: 0.9rem;
			text-align: center;
			margin-top: .5rem;
		}

		.disabled-link {
			pointer-events: none;   /* Prevent clicking */
			color: #aaa !important; /* Greyed out look */
			text-decoration: none;
		}
	</style>
</head>

<body>

	<div class="container d-flex justify-content-center align-items-center min-vh-100">
		<div class="row auth-card w-100" style="max-width: 1100px;">
			<!-- Left Section -->
			<div class="col-md-6 auth-left">
				<div class="text-center mb-4">
					<img src="{{ url('logo.png') }}" alt="Logo" class="mb-3">
					<h4>{{ __("Confirm OTP") }}</h4>
				</div>

				<form id="otpForm" method="POST" action="{{ $confirmation_link }}">
					@csrf

					<input type="hidden" name="username" value="{{ session('username') }}" />
					@if(request()->routeIs('*.confirm-otp'))
					<input type="hidden" name="password" value="{{ session('password') }}" />
					@endif
					<input type="hidden" name="channel" value="{{ session('channel') }}" />

					<p class="text-center text-muted mb-4">
						@if(session('channel') == 'email')
						{{ __('Enter the verification code sent to your email to confirm your identity.') }}
						@else
						{{ __('Enter the verification code sent to your phone number to confirm your identity.') }}
						@endif
					</p>

					<div class="otp-inputs" aria-label="OTP inputs" dir="ltr">
						<input inputmode="numeric" pattern="[0-9]*" type="text" maxlength="1" name="otp[]" class="form-control">
						<input inputmode="numeric" pattern="[0-9]*" type="text" maxlength="1" name="otp[]" class="form-control">
						<input inputmode="numeric" pattern="[0-9]*" type="text" maxlength="1" name="otp[]" class="form-control">
						<input inputmode="numeric" pattern="[0-9]*" type="text" maxlength="1" name="otp[]" class="form-control">
						<input inputmode="numeric" pattern="[0-9]*" type="text" maxlength="1" name="otp[]" class="form-control">
						<input inputmode="numeric" pattern="[0-9]*" type="text" maxlength="1" name="otp[]" class="form-control">
					</div>

					<div id="otp-error"></div>
					<div id="otp-success"></div>
					
					@if($can_resend)
					<div class="resend-text">
						@if($wait_seconds)
						<span id="timer">{{ $wait_seconds }}</span>
						@endif
						<a href="#" id="resend-link" class="@if($wait_seconds) disabled-link @endif">{{ __('Click here to resend') }}</a>
					</div>
					@endif

					<button type="submit" id="confirmBtn" class="btn btn-submit w-100 mt-3" disabled>
						{{ __('Confirm') }}
						<span class="spinner-border spinner-border-sm ms-2 d-none" role="status" aria-hidden="true"></span>
					</button>
				</form>
			</div>

			<!-- Right Section -->
			<div class="col-md-6 auth-right d-none d-md-block">
				<div class="auth-right-content">
					<img src="{{ url('background_top.png') }}" alt="Benaa Logo">
					<p class="fs-5 mt-3">{{ __("An integrated system to achieve the highest levels of productivity.") }}</p>
				</div>
			</div>
		</div>
	</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
	// ---------- helpers ----------
	function normalizeDigits(str) {
		const map = {
			'٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
			'٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9',
			'۰': '0', '۱': '1', '۲': '2', '۳': '3', '۴': '4',
			'۵': '5', '۶': '6', '۷': '7', '۸': '8', '۹': '9'
		};
		return str.split("").map(ch => map[ch] ?? ch).join("").replace(/\D/g, '');
	}

	// ---------- OTP inputs ----------
	(function () {
		const form = document.getElementById('otpForm');
		const inputs = Array.from(document.querySelectorAll('.otp-inputs input'));
		const errorEl = document.getElementById('otp-error');
		const successEl = document.getElementById('otp-success');
		const confirmBtn = document.getElementById('confirmBtn');
		const spinner = confirmBtn.querySelector('.spinner-border');

		function checkOtpFilled() {
			const filled = inputs.every(i => i.value.trim() !== '');
			confirmBtn.disabled = !filled;
		}
		inputs.forEach(i => i.addEventListener('input', checkOtpFilled));

		if (inputs.length) {
			inputs[0].focus();
			inputs.forEach(i => i.addEventListener('focus', () => i.select()));
		}

		inputs.forEach((input, idx) => {
			input.addEventListener('paste', (e) => {
				e.preventDefault();
				const digits = normalizeDigits((e.clipboardData || window.clipboardData).getData('text'));
				if (!digits) return;
				for (let i = 0; i < digits.length && (idx + i) < inputs.length; i++) {
					inputs[idx + i].value = digits.charAt(i);
				}
				const focusIndex = Math.min(inputs.length - 1, idx + digits.length);
				inputs[focusIndex].focus();
				checkOtpFilled();
			});

			input.addEventListener('input', (e) => {
				let value = normalizeDigits(e.target.value);
				if (!value) {
					e.target.value = '';
					checkOtpFilled();
					return;
				}
				e.target.value = value.charAt(0);
				if (value.length > 1) {
					for (let k = 1; k < value.length && (idx + k) < inputs.length; k++) {
						inputs[idx + k].value = value.charAt(k);
					}
					const nextFocus = Math.min(inputs.length - 1, idx + value.length);
					inputs[nextFocus].focus();
				} else {
					if (idx + 1 < inputs.length) inputs[idx + 1].focus();
				}
				checkOtpFilled();
			});

			input.addEventListener('keydown', (e) => {
				if (e.key === 'Backspace') {
					if (input.value === '' && idx > 0) {
						inputs[idx - 1].focus();
						inputs[idx - 1].value = '';
					} else {
						input.value = '';
					}
					e.preventDefault();
					checkOtpFilled();
				}
				if (e.key === 'ArrowLeft' && idx > 0) {
					inputs[idx - 1].focus();
					e.preventDefault();
				}
				if (e.key === 'ArrowRight' && idx < inputs.length - 1) {
					inputs[idx + 1].focus();
					e.preventDefault();
				}
			});
		});

		form.addEventListener('submit', function (e) {
			e.preventDefault();
			confirmBtn.disabled = true;
			spinner.classList.remove('d-none');

			const url = form.action;
			const formData = new FormData(form);
			const otp = inputs.map(i => i.value.trim()).join('');
			formData.append('otp', otp);

			fetch(url, {
				method: 'POST',
				headers: {
					'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
					'Accept': 'application/json'
				},
				body: formData
			})
				.then(async response => {
					if (response.ok) {
						window.location.href = "{{ $success_link }}";
						return;
					}
					const data = await response.json();
					if (data.errors) {
						errorEl.style.display = 'block';
						errorEl.innerText = Object.values(data.errors).flat().join("\n");
						successEl.style.display = 'none';
						inputs.forEach(i => i.value = '');
						inputs[0].focus();
						checkOtpFilled();
					}
				})
				.catch(err => {
					console.error(err);
					alert("Something went wrong. Please try again.");
					inputs.forEach(i => i.value = '');
					inputs[0].focus();
					checkOtpFilled();
				})
				.finally(() => spinner.classList.add('d-none'));
		});
	})();

	// ---------- Timer + Resend ----------
	(function () {
		let timerElement = document.getElementById("timer");
		let resendLink = document.getElementById("resend-link");
		let resendText = document.querySelector(".resend-text");
		let countdown = null;
		let resendSpinner = null;

		// create spinner if not exists
		if (resendLink) {
			resendSpinner = document.createElement("span");
			resendSpinner.className = "spinner-border spinner-border-sm ms-2 d-none";
			resendSpinner.setAttribute("role", "status");
			resendSpinner.setAttribute("aria-hidden", "true");
			resendLink.insertAdjacentElement("afterend", resendSpinner);
		}

		function ensureTimerElement() {
			if (!timerElement) {
				timerElement = document.createElement("span");
				timerElement.id = "timer";
				if (resendLink) {
					resendLink.insertAdjacentElement("beforebegin", timerElement);
					timerElement.style.marginRight = "0.5rem";
				} else if (resendText) {
					resendText.prepend(timerElement);
				}
			}
		}

		function formatTime(seconds) {
			const min = String(Math.floor(seconds / 60)).padStart(2, "0");
			const sec = String(seconds % 60).padStart(2, "0");
			return `${min}:${sec}`;
		}

		function startTimer(duration) {
			if (countdown) clearInterval(countdown);

			ensureTimerElement();
			let time = parseInt(duration, 10) || 0;

			timerElement.style.display = "inline";
			timerElement.textContent = formatTime(time);
			resendLink.classList.add("disabled-link");

			countdown = setInterval(() => {
				time--;
				if (time <= 0) {
					clearInterval(countdown);
					timerElement.style.display = "none";
					resendLink.classList.remove("disabled-link");
				} else {
					timerElement.textContent = formatTime(time);
				}
			}, 1000);
		}

		if (resendLink) {
			resendLink.addEventListener("click", function (e) {
				e.preventDefault();
				if (resendLink.classList.contains("disabled-link")) return;

				resendLink.classList.add("disabled-link");
				resendSpinner.classList.remove("d-none");

				fetch("{{ $resend_link }}", {
					method: "POST",
					headers: {
						"X-CSRF-TOKEN": document.querySelector('input[name="_token"]').value,
						"Accept": "application/json",
						"Content-Type": "application/json"
					},
					body: JSON.stringify({ username: "{{ session('username') }}" })
				})
					.then(res => res.json())
					.then(data => {
						if (data && data.data) {
							const errorEl = document.getElementById("otp-error");
							const successEl = document.getElementById("otp-success");
							successEl.style.display = 'block';
							successEl.innerText = "{{ __('otp was sent successfully.') }}";
							errorEl.style.display = 'none';

							if (data.data.can_resend) {
								startTimer(59);
							} else {
								resendText.style.display = "none";
							}
						} else {
							resendLink.classList.remove("disabled-link");
						}
					})
					.catch(err => {
						console.error(err);
						resendLink.classList.remove("disabled-link");
					})
					.finally(() => {
						resendSpinner.classList.add("d-none");
					});
			});
		}

		// initial server-side countdown
		@if(!empty($wait_seconds))
			startTimer({{ (int)$wait_seconds }});
		@endif
	})();
</script>



</body>
</html>
